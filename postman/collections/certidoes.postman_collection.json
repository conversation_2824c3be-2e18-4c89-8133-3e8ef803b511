{"info": {"_postman_id": "9c27b752-72b6-422a-8bee-5cebf1881e70", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "9829595", "_collection_link": "https://universal-crescent-545509.postman.co/workspace/SIAP-AL~a14e6d43-fbf6-40a0-a456-d6f8119775d6/collection/9829595-9c27b752-72b6-422a-8bee-5cebf1881e70?action=share&source=collection_link&creator=9829595"}, "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text"}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}], "url": "{{base_url}}/"}, "response": []}, {"name": "Listar 1 Certidão (certidaoShow)", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/140"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/41"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 16:11:30 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "Xc4f8c2344536681d707d6a561b14793f"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 18:11:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 41,\n        \"modelo\": {\n            \"id\": 2,\n            \"nome\": \"Modelo Analise Teste\",\n            \"data_inicio\": \"2023-01-01T03:00:00.000000Z\",\n            \"data_fim\": \"2030-01-01T03:00:00.000000Z\",\n            \"tipo_analise\": \"governo\",\n            \"esfera\": 2,\n            \"diretoria_id\": 1,\n            \"created_at\": \"2025-02-17T14:26:11.000000Z\",\n            \"updated_at\": \"2025-02-17T14:26:11.000000Z\",\n            \"categoria_documento\": 2\n        },\n        \"cidade\": \"<PERSON><PERSON><PERSON>\",\n        \"responsavel\": \"Administrador\",\n        \"data_criacao\": \"2025-02-25T19:11:18.000000Z\",\n        \"data_conclusao\": null,\n        \"assinatura\": null,\n        \"status\": \"aberta\",\n        \"created_at\": null,\n        \"updated_at\": null\n    }\n}"}, {"name": "Fail", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/1"}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 16:12:29 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "phpdebugbar-id", "value": "Xe79e0d6fb353ac71ce98fee57791f09a"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 18:12:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"No query results for model [App\\\\Models\\\\Sapc\\\\CertidaoPVL] 1\",\n    \"exception\": \"Symfony\\\\Component\\\\HttpKernel\\\\Exception\\\\NotFoundHttpException\",\n    \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php\",\n    \"line\": 487,\n    \"trace\": [\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php\",\n            \"line\": 463,\n            \"function\": \"prepareException\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Exceptions\\\\Handler\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php\",\n            \"line\": 51,\n            \"function\": \"render\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Exceptions\\\\Handler\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 188,\n            \"function\": \"handleException\",\n            \"class\": \"Illuminate\\\\Routing\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 159,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 125,\n            \"function\": \"handleRequest\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 87,\n            \"function\": \"handleRequestUsingNamedLimiter\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php\",\n            \"line\": 25,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 144,\n            \"function\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\{closure}\",\n            \"class\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php\",\n            \"line\": 24,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 805,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 784,\n            \"function\": \"runRouteWithinStack\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 748,\n            \"function\": \"runRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 737,\n            \"function\": \"dispatchToRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 200,\n            \"function\": \"dispatch\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 144,\n            \"function\": \"Illuminate\\\\Foundation\\\\Http\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/nova/src/Http/Middleware/ServeNova.php\",\n            \"line\": 23,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Laravel\\\\Nova\\\\Http\\\\Middleware\\\\ServeNova\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php\",\n            \"line\": 66,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/app/Http/Middleware/RedirectNovaCreate.php\",\n            \"line\": 23,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"App\\\\Http\\\\Middleware\\\\RedirectNovaCreate\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php\",\n            \"line\": 121,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php\",\n            \"line\": 64,\n            \"function\": \"handleStatefulRequest\",\n            \"class\": \"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php\",\n            \"line\": 21,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php\",\n            \"line\": 31,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php\",\n            \"line\": 21,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php\",\n            \"line\": 40,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php\",\n            \"line\": 27,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php\",\n            \"line\": 99,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php\",\n            \"line\": 62,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Http\\\\Middleware\\\\HandleCors\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php\",\n            \"line\": 39,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Http\\\\Middleware\\\\TrustProxies\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 175,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 144,\n            \"function\": \"sendRequestThroughRouter\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/public/index.php\",\n            \"line\": 51,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        }\n    ]\n}"}, {"name": "Listar 1 Certidão (certidaoShow)", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/140"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 08:57:11 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 10:57:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 140,\n        \"modelo\": {\n            \"id\": 7,\n            \"nome\": \"Certidão 2025\",\n            \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n            \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n            \"tipo_analise\": \"governo\",\n            \"esfera\": 2,\n            \"diretoria_id\": 4,\n            \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n            \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n            \"categoria_documento\": 2\n        },\n        \"cidade\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"responsavel\": \"ednei\",\n        \"data_criacao\": \"2025-03-08T16:33:03.000000Z\",\n        \"data_conclusao\": null,\n        \"assinatura\": null,\n        \"situacao\": \"Aberta\",\n        \"esfera\": \"Municipal\",\n        \"created_at\": \"2025-03-08T16:33:03.000000Z\",\n        \"updated_at\": \"2025-03-08T16:33:03.000000Z\"\n    }\n}"}]}, {"name": "Listar Formularios Certidões( certidaoFormularios )", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formularios"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/41/formularios"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 16:17:47 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "Xe74ab886ce08aaf933d4ec7087e06de4"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 18:17:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": []\n}"}, {"name": "Listar Formularios Certidões( certidaoFormularios )", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formularios"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 08:58:12 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 10:58:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 317,\n            \"certidao_id\": 145,\n            \"modelo_formulario_id\": 35,\n            \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n            \"data_preenchimento\": null,\n            \"texto\": \"<p>CERTIFICAMOS que atendendo a solicitação formulada pelo Senhor MARCELO BELTRÃO SIQUEIRA, Prefeito do Município de <strong>Jacuípe/</strong>AL, através do OfÍcio n' 0O2 12023, datado de 1110412023, protocolado neste Tribunal de Contas do Estado, sob no TC73512023, pelo MunicÍpio de <strong><PERSON><PERSON><PERSON><PERSON><PERSON></strong>/AL, para obtenção junto à Secretaria do Tesouro Nacional - STN, com o objetivo de realizar operação de crédito, junto a Caixa Econômica Federal, Agente Financeiro da União, no valor de R$ <strong>0,00</strong>, destinados a investimentos no âmbito do Programa/Linha de Financiamento a lnfraestrutura e Saneamento - FlNlsA\\\"/Apoio Financeiro nos Termos da Resolução CMN No 4.589 de 2910612017 , voltados na melhoria da ef iciência, qualidade e transparência da gestão pública, em consonância con o parágrafo 10 da Lei Complementar Federal (LRF), No \\\"101, de 0410512000, o ATESTE de ctrmprinento ao art. 167, inciso lll e 'Art. 167-A\\\", da Constituição Federal-CF/88 (ou parágrafo 20 do artigo 12 da LRF) e artigos 33 e 37, da LRF, referindo-se ao ente de forma global. Com base no Relatório realizado sobre as contas do último exercício analisado, 2018 e nas publicações pelo Poder Executivo e dos Relatórios Resumidos da Execção Oçamentária dos '1o, 2, 3o, 4o, 50 e 60 bimestres e Relatórios de Gestão Fiscal dos 1o, 2o e 3o quadrimestres, referentes aos exercícios financeiros de: 20í8, 2019, 2020,202í e Relatórios Resumidos do 1o, ?,30, 4o,S e 60 bimestres, como também o de Gestâo Fiscal em relação ao 1o,2o e 3o quadrimestres de 2022, e 1o bimestre de 2023 para fins de cumprimento às Resoluções no 043/2001 e no 48l2oo7 do Senado Federal e que o MUNICíPO DE <strong>Jacuípe/</strong>AL., apresentou suas Contas, comunicamos ainda que, em relação às contas da Gestão Fiscal.<br><br><strong>0,00</strong></p>\",\n            \"versao\": \"1\",\n            \"user_id\": 3192,\n            \"created_at\": null,\n            \"updated_at\": null,\n            \"exercicio\": null,\n            \"nome\": \"Certidao Introdução\",\n            \"nome_formulario\": \"Certidao Introdução\",\n            \"status\": \"aguardando_inicio\",\n            \"status_descricao\": \"Aguardando início\",\n            \"user_finalizado\": null,\n            \"texto_auto_save\": null\n        },\n        {\n            \"id\": 319,\n            \"certidao_id\": 145,\n            \"modelo_formulario_id\": 37,\n            \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n            \"data_preenchimento\": null,\n            \"texto\": \"<h1>Exercício Analisado</h1><p>Exercicio Analisado <strong>2023</strong>: o Município Cumpriu com o disposto nos seguintes artigos da 6n@ay88:Art.167,llle..Art.í67.A',,conformeeStabelecidonoart.53,§1o, inciso l, da Lei Complementar no 101/2000: Cumpriu com o disposto nos seguintes artigos da Constituição Federal/88: art. í98, com 18,01% (dezoito vkgula zero um por cento) da Receita Líquida de lmpostos; an.212, com 30,64% (trinta vírgula sessenta e quatro por cento), da Receita LQuida de lmpostos. Cumpriu com o disposto nos seguintes artigos da Lei Complementar no 10'1/2000: Art. 1í, art. 23, art. 33, art. 37, art. 52 e art. 55, § 2\\\". Recêita Corrente Líquida - RCL, no valor de R$ <strong>$_rreo{RREO-Anexo 01_ReceitasCorrentes_PREVIS\\\\u00c3O INICIAL}</strong>. Despesa com Pessoal, Cumpriu, aplicando o valor de R$ 98.852.707,52 (noventa e oito milhões, oitocentos e cinquenta e dois mil, setecentos e sete reais e cinquenta e dois centavos), perfazendo um percentual de 40,08% (quarenta v írgula zero oito por cento). O Podêr Executivo, Cumpriu, sendo aplicado o valor de R$ <strong>1.608.264,00</strong>, perfazendo um percentual csn 38,87% (trinta e oito vÍ'gula oitenta e sete por cento). O Poder Legislativo Cumpriu, no valor de R$ 2.983.í99,33 (dois milhões, novecentos e oitenta e três mil, cenlo e noventa e nove reais e trinta e três centavos), correspondendo ao percentualde1,21%(umvírgulavintee</p>\",\n            \"versao\": \"1\",\n            \"user_id\": 3192,\n            \"created_at\": null,\n            \"updated_at\": null,\n            \"exercicio\": \"2023\",\n            \"nome\": \"Formulario Analisado\",\n            \"nome_formulario\": \"2023 - Formulario Analisado\",\n            \"status\": \"aguardando_inicio\",\n            \"status_descricao\": \"Aguardando início\",\n            \"user_finalizado\": null,\n            \"texto_auto_save\": null\n        },\n        {\n            \"id\": 318,\n            \"certidao_id\": 145,\n            \"modelo_formulario_id\": 36,\n            \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n            \"data_preenchimento\": null,\n            \"texto\": \"<h1>Exercicio não analisado</h1><p>Exercício em análise - <strong>2022</strong> o Municí;oio Cumpriu com o disposto nos seguintes artigos da onstituição Federa : 4rt.167, lll e \\\"Art. 167-4\\\", conforme estabelecido no art. 53, § ío, inciso l, da Lei Complementar no 101/2000: Cumpriu com o disposto nos seguintes artigos da Constituição Federal/88: art. 198, com 17,3'lo/o (dezessete vÍrgula trinta e um por cento) da Receita Líquida de lmpostos; art.212, com 25,15% (vinte e cinco vírgula quinze por cento), da Receita Líquida de lmpostos. Cumpriu com o disposto nos seguintes artigos da Lei Complementar no 10í/2000: Art. 11, art.23, art. 33, art. 37, arl. 52 e art. 55, § 2. Receita Corrente Líquida - RCL, no valor de R$<strong> 5.429.502,53</strong> Despesa com Pessoal, Cumpriu, aplicando o valor de R$ 9í.2í2.453,39 (noventa e um milhões, duzentos e doze mil, quatrocentos e cinquenta e três reais e trinta e nove centavos), peíazendo um percentual de 4,500Â (quarenta e quatro vírgula cinquenta por cento). O Poder Executivo, Cumpriu, sendo aplicado o valor de R$ <strong>248.421.000,00</strong>O Poder Legislativo Cumpriu, no v 223.633, (trêsf Certidão Prefeitura de <strong>Jacuípe</strong>/Al - TC-735/2023. alordeR$3. ftu Página 1de 3 vinte reais e quatro centavos), perfazendo um percentual de 42,93o/o (quarenta e dois i:Elt.i iiüra I \\\\:?É,. TCE.AL TRIBUNAL DE CONTAS DO ESTADO DE ALAGOAS milhões, duzentos e vinte e três mil, seiscentos e trinta e três reais e trinta e cinco centavos), correspondendo ao percentual de 1,57% (um vÍ.gula cinquenta e sete por cento)'</p>\",\n            \"versao\": \"1\",\n            \"user_id\": 3192,\n            \"created_at\": null,\n            \"updated_at\": null,\n            \"exercicio\": \"2022\",\n            \"nome\": \"Formulario Nao Analisado\",\n            \"nome_formulario\": \"2022 - Formulario Nao Analisado\",\n            \"status\": \"aguardando_inicio\",\n            \"status_descricao\": \"Aguardando início\",\n            \"user_finalizado\": null,\n            \"texto_auto_save\": null\n        },\n        {\n            \"id\": 320,\n            \"certidao_id\": 145,\n            \"modelo_formulario_id\": 36,\n            \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n            \"data_preenchimento\": null,\n            \"texto\": \"<h1>Exercicio não analisado</h1><p>Exercício em análise - <strong>2024</strong> o Municí;oio Cumpriu com o disposto nos seguintes artigos da onstituição Federa : 4rt.167, lll e \\\"Art. 167-4\\\", conforme estabelecido no art. 53, § ío, inciso l, da Lei Complementar no 101/2000: Cumpriu com o disposto nos seguintes artigos da Constituição Federal/88: art. 198, com 17,3'lo/o (dezessete vÍrgula trinta e um por cento) da Receita Líquida de lmpostos; art.212, com 25,15% (vinte e cinco vírgula quinze por cento), da Receita Líquida de lmpostos. Cumpriu com o disposto nos seguintes artigos da Lei Complementar no 10í/2000: Art. 11, art.23, art. 33, art. 37, arl. 52 e art. 55, § 2. Receita Corrente Líquida - RCL, no valor de R$<strong> 4.543.945,31</strong> Despesa com Pessoal, Cumpriu, aplicando o valor de R$ 9í.2í2.453,39 (noventa e um milhões, duzentos e doze mil, quatrocentos e cinquenta e três reais e trinta e nove centavos), peíazendo um percentual de 4,500Â (quarenta e quatro vírgula cinquenta por cento). O Poder Executivo, Cumpriu, sendo aplicado o valor de R$ <strong>327.067.500,00</strong>O Poder Legislativo Cumpriu, no v 223.633, (trêsf Certidão Prefeitura de <strong>Jacuípe</strong>/Al - TC-735/2023. alordeR$3. ftu Página 1de 3 vinte reais e quatro centavos), perfazendo um percentual de 42,93o/o (quarenta e dois i:Elt.i iiüra I \\\\:?É,. TCE.AL TRIBUNAL DE CONTAS DO ESTADO DE ALAGOAS milhões, duzentos e vinte e três mil, seiscentos e trinta e três reais e trinta e cinco centavos), correspondendo ao percentual de 1,57% (um vÍ.gula cinquenta e sete por cento)'</p>\",\n            \"versao\": \"1\",\n            \"user_id\": 3192,\n            \"created_at\": null,\n            \"updated_at\": null,\n            \"exercicio\": \"2024\",\n            \"nome\": \"Formulario Nao Analisado\",\n            \"nome_formulario\": \"2024 - Formulario Nao Analisado\",\n            \"status\": \"aguardando_inicio\",\n            \"status_descricao\": \"Aguardando início\",\n            \"user_finalizado\": null,\n            \"texto_auto_save\": null\n        }\n    ]\n}"}]}, {"name": "Listar Status das Certidões (listCertidaoStatus)", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/status"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/status"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 16:25:49 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "X8165f97fc94631a6a56fd26f294d5319"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 18:25:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"aberta\": \"<PERSON><PERSON>\",\n    \"iniciada\": \"Iniciada\",\n    \"emitida\": \"Emitida\",\n    \"vencida\": \"Vencida\"\n}"}, {"name": "Listar Status das Certidões (listCertidaoStatus)", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/status"}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 08:58:36 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 10:58:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Connection", "value": "close"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"SQLSTATE[22P02]: Invalid text representation: 7 ERROR:  invalid input syntax for type bigint: \\\"status\\\" (Connection: pgsql, SQL: select * from \\\"sapc\\\".\\\"certidao_pvl\\\" where \\\"id\\\" = status limit 1)\",\n    \"exception\": \"Illuminate\\\\Database\\\\QueryException\",\n    \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Connection.php\",\n    \"line\": 829,\n    \"trace\": [\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Connection.php\",\n            \"line\": 783,\n            \"function\": \"runQueryCallback\",\n            \"class\": \"Illuminate\\\\Database\\\\Connection\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Connection.php\",\n            \"line\": 414,\n            \"function\": \"run\",\n            \"class\": \"Illuminate\\\\Database\\\\Connection\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php\",\n            \"line\": 2913,\n            \"function\": \"select\",\n            \"class\": \"Illuminate\\\\Database\\\\Connection\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php\",\n            \"line\": 2902,\n            \"function\": \"runSelect\",\n            \"class\": \"Illuminate\\\\Database\\\\Query\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php\",\n            \"line\": 3456,\n            \"function\": \"Illuminate\\\\Database\\\\Query\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Database\\\\Query\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php\",\n            \"line\": 2901,\n            \"function\": \"onceWithColumns\",\n            \"class\": \"Illuminate\\\\Database\\\\Query\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php\",\n            \"line\": 739,\n            \"function\": \"get\",\n            \"class\": \"Illuminate\\\\Database\\\\Query\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php\",\n            \"line\": 723,\n            \"function\": \"getModels\",\n            \"class\": \"Illuminate\\\\Database\\\\Eloquent\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php\",\n            \"line\": 333,\n            \"function\": \"get\",\n            \"class\": \"Illuminate\\\\Database\\\\Eloquent\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php\",\n            \"line\": 2047,\n            \"function\": \"first\",\n            \"class\": \"Illuminate\\\\Database\\\\Eloquent\\\\Builder\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php\",\n            \"line\": 61,\n            \"function\": \"resolveRouteBinding\",\n            \"class\": \"Illuminate\\\\Database\\\\Eloquent\\\\Model\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 959,\n            \"function\": \"resolveForRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\ImplicitRouteBinding\",\n            \"type\": \"::\"\n        },\n        {\n            \"function\": \"Illuminate\\\\Routing\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 961,\n            \"function\": \"call_user_func\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php\",\n            \"line\": 41,\n            \"function\": \"substituteImplicitBindings\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 159,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 125,\n            \"function\": \"handleRequest\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 87,\n            \"function\": \"handleRequestUsingNamedLimiter\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php\",\n            \"line\": 25,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 144,\n            \"function\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\{closure}\",\n            \"class\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php\",\n            \"line\": 24,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 805,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 784,\n            \"function\": \"runRouteWithinStack\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 748,\n            \"function\": \"runRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 737,\n            \"function\": \"dispatchToRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 200,\n            \"function\": \"dispatch\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 144,\n            \"function\": \"Illuminate\\\\Foundation\\\\Http\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/nova/src/Http/Middleware/ServeNova.php\",\n            \"line\": 23,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Laravel\\\\Nova\\\\Http\\\\Middleware\\\\ServeNova\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php\",\n            \"line\": 59,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/app/Http/Middleware/RedirectNovaCreate.php\",\n            \"line\": 23,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"App\\\\Http\\\\Middleware\\\\RedirectNovaCreate\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php\",\n            \"line\": 121,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php\",\n            \"line\": 64,\n            \"function\": \"handleStatefulRequest\",\n            \"class\": \"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php\",\n            \"line\": 21,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php\",\n            \"line\": 31,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php\",\n            \"line\": 21,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php\",\n            \"line\": 40,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php\",\n            \"line\": 27,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php\",\n            \"line\": 99,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php\",\n            \"line\": 62,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Http\\\\Middleware\\\\HandleCors\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php\",\n            \"line\": 39,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Http\\\\Middleware\\\\TrustProxies\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 175,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 144,\n            \"function\": \"sendRequestThroughRouter\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/public/index.php\",\n            \"line\": 51,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        }\n    ]\n}"}]}, {"name": "Listar Certidões (certidoesTodas)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidoes"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidoes"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Tu<PERSON>, 25 Feb 2025 16:28:15 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "X6c2c3d92ec9ff27f7124d9bb6e99ec40"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 18:28:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 41,\n            \"modelo\": {\n                \"id\": 2,\n                \"nome\": \"Modelo Analise Teste\",\n                \"data_inicio\": \"2023-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-01-01T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 1,\n                \"created_at\": \"2025-02-17T14:26:11.000000Z\",\n                \"updated_at\": \"2025-02-17T14:26:11.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Mace<PERSON>ó\",\n            \"responsavel\": \"Administrador\",\n            \"data_criacao\": \"2025-02-25T19:11:18.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"status\": \"aberta\",\n            \"created_at\": null,\n            \"updated_at\": null\n        }\n    ],\n    \"links\": {\n        \"first\": \"http://localhost:8084/api/certidao-pvl/certidoes?page=1\",\n        \"last\": \"http://localhost:8084/api/certidao-pvl/certidoes?page=1\",\n        \"prev\": null,\n        \"next\": null\n    },\n    \"meta\": {\n        \"current_page\": 1,\n        \"from\": 1,\n        \"last_page\": 1,\n        \"links\": [\n            {\n                \"url\": null,\n                \"label\": \"&laquo; Anterior\",\n                \"active\": false\n            },\n            {\n                \"url\": \"http://localhost:8084/api/certidao-pvl/certidoes?page=1\",\n                \"label\": \"1\",\n                \"active\": true\n            },\n            {\n                \"url\": null,\n                \"label\": \"Próximo &raquo;\",\n                \"active\": false\n            }\n        ],\n        \"path\": \"http://localhost:8084/api/certidao-pvl/certidoes\",\n        \"per_page\": 20,\n        \"to\": 1,\n        \"total\": 1\n    }\n}"}, {"name": "Listar Certidões (certidoesTodas)", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidoes"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:01:37 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:01:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": [\n        {\n            \"id\": 145,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:41:58.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:41:58.000000Z\",\n            \"updated_at\": \"2025-03-08T16:41:58.000000Z\"\n        },\n        {\n            \"id\": 144,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:41:16.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:41:16.000000Z\",\n            \"updated_at\": \"2025-03-08T16:41:16.000000Z\"\n        },\n        {\n            \"id\": 143,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:38:13.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:38:13.000000Z\",\n            \"updated_at\": \"2025-03-08T16:38:13.000000Z\"\n        },\n        {\n            \"id\": 142,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:37:16.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:37:16.000000Z\",\n            \"updated_at\": \"2025-03-08T16:37:16.000000Z\"\n        },\n        {\n            \"id\": 141,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:33:38.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:33:38.000000Z\",\n            \"updated_at\": \"2025-03-08T16:33:38.000000Z\"\n        },\n        {\n            \"id\": 140,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:33:03.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:33:03.000000Z\",\n            \"updated_at\": \"2025-03-08T16:33:03.000000Z\"\n        },\n        {\n            \"id\": 139,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:32:26.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:32:26.000000Z\",\n            \"updated_at\": \"2025-03-08T16:32:26.000000Z\"\n        },\n        {\n            \"id\": 138,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:28:44.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:28:44.000000Z\",\n            \"updated_at\": \"2025-03-08T16:28:44.000000Z\"\n        },\n        {\n            \"id\": 137,\n            \"modelo\": {\n                \"id\": 7,\n                \"nome\": \"Certidão 2025\",\n                \"data_inicio\": \"2025-01-01T03:00:00.000000Z\",\n                \"data_fim\": \"2030-12-31T03:00:00.000000Z\",\n                \"tipo_analise\": \"governo\",\n                \"esfera\": 2,\n                \"diretoria_id\": 4,\n                \"created_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"updated_at\": \"2025-02-27T13:03:42.000000Z\",\n                \"categoria_documento\": 2\n            },\n            \"cidade\": \"Arapiraca\",\n            \"responsavel\": \"ednei\",\n            \"data_criacao\": \"2025-03-08T16:22:16.000000Z\",\n            \"data_conclusao\": null,\n            \"assinatura\": null,\n            \"situacao\": \"Aberta\",\n            \"esfera\": \"Municipal\",\n            \"created_at\": \"2025-03-08T16:22:16.000000Z\",\n            \"updated_at\": \"2025-03-08T16:22:16.000000Z\"\n        }\n    ],\n    \"links\": {\n        \"first\": \"http://localhost:8084/api/certidao-pvl/certidoes?page=1\",\n        \"last\": \"http://localhost:8084/api/certidao-pvl/certidoes?page=1\",\n        \"prev\": null,\n        \"next\": null\n    },\n    \"meta\": {\n        \"current_page\": 1,\n        \"from\": 1,\n        \"last_page\": 1,\n        \"links\": [\n            {\n                \"url\": null,\n                \"label\": \"&laquo; Anterior\",\n                \"active\": false\n            },\n            {\n                \"url\": \"http://localhost:8084/api/certidao-pvl/certidoes?page=1\",\n                \"label\": \"1\",\n                \"active\": true\n            },\n            {\n                \"url\": null,\n                \"label\": \"Próximo &raquo;\",\n                \"active\": false\n            }\n        ],\n        \"path\": \"http://localhost:8084/api/certidao-pvl/certidoes\",\n        \"per_page\": 20,\n        \"to\": 9,\n        \"total\": 9\n    }\n}"}]}, {"name": "Download da Certidao (certidaoPVLDownload) (pdf sem conteudo)", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/140/download"}, "response": []}, {"name": "Listar certidaoPVLFormulario Certidões( formularioShow )", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/44/formulario/28"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 20:46:08 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "X07e4529a394e33a6f6d95850f93d10da"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 22:46:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 28,\n        \"certidao_id\": 44,\n        \"modelo_formulario_id\": 3,\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n        \"texto\": \"TExto 1\",\n        \"versao\": \"3\",\n        \"user_id\": 1,\n        \"created_at\": null,\n        \"updated_at\": \"2025-02-25T19:47:23.000000Z\",\n        \"nome\": \"Formulario Não Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": null\n    }\n}"}, {"name": "Listar certidaoPVLFormulario Certidões( formularioShow )", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:02:55 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:02:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 318,\n        \"certidao_id\": 145,\n        \"modelo_formulario_id\": 36,\n        \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n        \"data_preenchimento\": null,\n        \"texto\": \"<h1><PERSON>er<PERSON>cio não analisado</h1><p>Exercício em análise - <strong>2022</strong> o Municí;oio Cumpriu com o disposto nos seguintes artigos da onstituição Federa : 4rt.167, lll e \\\"Art. 167-4\\\", conforme estabelecido no art. 53, § ío, inciso l, da Lei Complementar no 101/2000: Cumpriu com o disposto nos seguintes artigos da Constituição Federal/88: art. 198, com 17,3'lo/o (dezessete vÍrgula trinta e um por cento) da Receita Líquida de lmpostos; art.212, com 25,15% (vinte e cinco vírgula quinze por cento), da Receita Líquida de lmpostos. Cumpriu com o disposto nos seguintes artigos da Lei Complementar no 10í/2000: Art. 11, art.23, art. 33, art. 37, arl. 52 e art. 55, § 2. Receita Corrente Líquida - RCL, no valor de R$<strong> 5.429.502,53</strong> Despesa com Pessoal, Cumpriu, aplicando o valor de R$ 9í.2í2.453,39 (noventa e um milhões, duzentos e doze mil, quatrocentos e cinquenta e três reais e trinta e nove centavos), peíazendo um percentual de 4,500Â (quarenta e quatro vírgula cinquenta por cento). O Poder Executivo, Cumpriu, sendo aplicado o valor de R$ <strong>248.421.000,00</strong>O Poder Legislativo Cumpriu, no v 223.633, (trêsf Certidão Prefeitura de <strong>Jacuípe</strong>/Al - TC-735/2023. alordeR$3. ftu Página 1de 3 vinte reais e quatro centavos), perfazendo um percentual de 42,93o/o (quarenta e dois i:Elt.i iiüra I \\\\:?É,. TCE.AL TRIBUNAL DE CONTAS DO ESTADO DE ALAGOAS milhões, duzentos e vinte e três mil, seiscentos e trinta e três reais e trinta e cinco centavos), correspondendo ao percentual de 1,57% (um vÍ.gula cinquenta e sete por cento)'</p>\",\n        \"versao\": \"1\",\n        \"user_id\": 3192,\n        \"created_at\": null,\n        \"updated_at\": null,\n        \"exercicio\": \"2022\",\n        \"nome\": \"Formulario Nao Analisado\",\n        \"nome_formulario\": \"2022 - Formulario Nao Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"status_descricao\": \"Aguardando início\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": null\n    }\n}"}]}, {"name": "Listar versão de formularios( formularioGetVersoes )", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/versoes"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/44/formulario/28/versoes"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 21:35:43 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "55"}, {"key": "phpdebugbar-id", "value": "Xadd53e6dcdd3b6367b99221941b75a88"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 23:35:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"current_page\": 1,\n    \"data\": [\n        {\n            \"id\": 2,\n            \"certidao_formulario_id\": 28,\n            \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n            \"texto\": \"TExto 1\",\n            \"versao\": 1,\n            \"user_id\": 1,\n            \"created_at\": null,\n            \"updated_at\": null\n        },\n        {\n            \"id\": 7,\n            \"certidao_formulario_id\": 28,\n            \"data_preenchimento\": null,\n            \"texto\": \"esse texto de exemplo testa a variavel \\\\310.693.685,43 outro exemplo \\\\$_rgf{RGF-Anexo 02_DespesaComPessoalBruta_<MR-11>} \\\\$_rgf{RGF-Anexo 03_DespesaComPessoalBruta_<MR-12>}\\n\\nesse texto de exemplo testa a variavel 2.362.348.470,00\",\n            \"versao\": 1,\n            \"user_id\": 1,\n            \"created_at\": \"2025-02-25T19:46:38.000000Z\",\n            \"updated_at\": \"2025-02-25T19:46:38.000000Z\"\n        },\n        {\n            \"id\": 8,\n            \"certidao_formulario_id\": 28,\n            \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n            \"texto\": \"TExto 1\",\n            \"versao\": 2,\n            \"user_id\": 1,\n            \"created_at\": \"2025-02-25T19:47:23.000000Z\",\n            \"updated_at\": \"2025-02-25T19:47:23.000000Z\"\n        },\n        {\n            \"id\": 9,\n            \"certidao_formulario_id\": 28,\n            \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n            \"texto\": \"TExto 1\",\n            \"versao\": 3,\n            \"user_id\": 1,\n            \"created_at\": \"2025-02-25T21:26:43.000000Z\",\n            \"updated_at\": \"2025-02-25T21:26:43.000000Z\"\n        },\n        {\n            \"id\": 10,\n            \"certidao_formulario_id\": 28,\n            \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n            \"texto\": \"TExto 1\",\n            \"versao\": 4,\n            \"user_id\": 1,\n            \"created_at\": \"2025-02-25T21:27:51.000000Z\",\n            \"updated_at\": \"2025-02-25T21:27:51.000000Z\"\n        }\n    ],\n    \"first_page_url\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/versoes?page=1\",\n    \"from\": 1,\n    \"last_page\": 1,\n    \"last_page_url\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/versoes?page=1\",\n    \"links\": [\n        {\n            \"url\": null,\n            \"label\": \"&laquo; Anterior\",\n            \"active\": false\n        },\n        {\n            \"url\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/versoes?page=1\",\n            \"label\": \"1\",\n            \"active\": true\n        },\n        {\n            \"url\": null,\n            \"label\": \"Próximo &raquo;\",\n            \"active\": false\n        }\n    ],\n    \"next_page_url\": null,\n    \"path\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/versoes\",\n    \"per_page\": 20,\n    \"prev_page_url\": null,\n    \"to\": 5,\n    \"total\": 5\n}"}, {"name": "Listar versão de formularios( formularioGetVersoes )", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/versoes"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:03:28 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:03:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": [],\n    \"links\": {\n        \"first\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/versoes?page=1\",\n        \"last\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/versoes?page=1\",\n        \"prev\": null,\n        \"next\": null\n    },\n    \"meta\": {\n        \"current_page\": 1,\n        \"from\": null,\n        \"last_page\": 1,\n        \"links\": [\n            {\n                \"url\": null,\n                \"label\": \"&laquo; Anterior\",\n                \"active\": false\n            },\n            {\n                \"url\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/versoes?page=1\",\n                \"label\": \"1\",\n                \"active\": true\n            },\n            {\n                \"url\": null,\n                \"label\": \"Próximo &raquo;\",\n                \"active\": false\n            }\n        ],\n        \"path\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/versoes\",\n        \"per_page\": 20,\n        \"to\": null,\n        \"total\": 0\n    }\n}"}]}, {"name": "Listar 1 versão de formularios( formularioGetVersao)", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/formulario/318/versao/10"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/formulario/28/versao/10"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 21:39:43 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "phpdebugbar-id", "value": "Xd91238639b0422323f74ab1b8e8085d5"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 23:39:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"id\": 10,\n    \"certidao_formulario_id\": 28,\n    \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n    \"texto\": \"TExto 1\",\n    \"versao\": 4,\n    \"user_id\": 1,\n    \"created_at\": \"2025-02-25T21:27:51.000000Z\",\n    \"updated_at\": \"2025-02-25T21:27:51.000000Z\",\n    \"diff_current\": \"TExto 1\",\n    \"diff_old\": \"TExto 1\",\n    \"formulario\": {\n        \"id\": 28,\n        \"certidao_id\": 44,\n        \"modelo_formulario_id\": 3,\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n        \"texto\": \"TExto 1\",\n        \"versao\": \"5\",\n        \"user_id\": 1,\n        \"created_at\": null,\n        \"updated_at\": \"2025-02-25T21:27:51.000000Z\",\n        \"nome\": \"Formulario Não Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": \"Texto auto Save\"\n    },\n    \"usuario\": null\n}"}, {"name": "Listar 1 versão de formularios( formularioGetVersao)", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/formulario/318/versao/10"}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:03:54 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "57"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:03:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"No query results for model [App\\\\Models\\\\Sapc\\\\CertidaoPVLFormularioVersao] 10\",\n    \"exception\": \"Symfony\\\\Component\\\\HttpKernel\\\\Exception\\\\NotFoundHttpException\",\n    \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php\",\n    \"line\": 487,\n    \"trace\": [\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php\",\n            \"line\": 463,\n            \"function\": \"prepareException\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Exceptions\\\\Handler\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php\",\n            \"line\": 51,\n            \"function\": \"render\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Exceptions\\\\Handler\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 188,\n            \"function\": \"handleException\",\n            \"class\": \"Illuminate\\\\Routing\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 159,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 125,\n            \"function\": \"handleRequest\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php\",\n            \"line\": 87,\n            \"function\": \"handleRequestUsingNamedLimiter\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php\",\n            \"line\": 25,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 144,\n            \"function\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\{closure}\",\n            \"class\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php\",\n            \"line\": 24,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 805,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 784,\n            \"function\": \"runRouteWithinStack\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 748,\n            \"function\": \"runRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php\",\n            \"line\": 737,\n            \"function\": \"dispatchToRoute\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 200,\n            \"function\": \"dispatch\",\n            \"class\": \"Illuminate\\\\Routing\\\\Router\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 144,\n            \"function\": \"Illuminate\\\\Foundation\\\\Http\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/nova/src/Http/Middleware/ServeNova.php\",\n            \"line\": 23,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Laravel\\\\Nova\\\\Http\\\\Middleware\\\\ServeNova\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php\",\n            \"line\": 59,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Barryvdh\\\\Debugbar\\\\Middleware\\\\InjectDebugbar\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/app/Http/Middleware/RedirectNovaCreate.php\",\n            \"line\": 23,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"App\\\\Http\\\\Middleware\\\\RedirectNovaCreate\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php\",\n            \"line\": 121,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php\",\n            \"line\": 64,\n            \"function\": \"handleStatefulRequest\",\n            \"class\": \"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php\",\n            \"line\": 21,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php\",\n            \"line\": 31,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ConvertEmptyStringsToNull\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php\",\n            \"line\": 21,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php\",\n            \"line\": 40,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TransformsRequest\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\TrimStrings\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php\",\n            \"line\": 27,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\ValidatePostSize\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php\",\n            \"line\": 99,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\PreventRequestsDuringMaintenance\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php\",\n            \"line\": 62,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Http\\\\Middleware\\\\HandleCors\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php\",\n            \"line\": 39,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 183,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Http\\\\Middleware\\\\TrustProxies\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\",\n            \"line\": 119,\n            \"function\": \"Illuminate\\\\Pipeline\\\\{closure}\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 175,\n            \"function\": \"then\",\n            \"class\": \"Illuminate\\\\Pipeline\\\\Pipeline\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\",\n            \"line\": 144,\n            \"function\": \"sendRequestThroughRouter\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        },\n        {\n            \"file\": \"/var/www/html/public/index.php\",\n            \"line\": 51,\n            \"function\": \"handle\",\n            \"class\": \"Illuminate\\\\Foundation\\\\Http\\\\Kernel\",\n            \"type\": \"->\"\n        }\n    ]\n}"}]}, {"name": "Listar Restaurar Versão de formularios( formularioRestoreVersao )", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/versao/2/restore"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/44/formulario/28/versao/2/restore"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 22:08:08 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "56"}, {"key": "phpdebugbar-id", "value": "X5e7a9e9017ffc9732d7c323a7befff3d"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Wed, 26 Feb 2025 00:08:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 28,\n        \"certidao_id\": 44,\n        \"modelo_formulario_id\": 3,\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n        \"texto\": \"TExto 1\",\n        \"versao\": 6,\n        \"user_id\": 1,\n        \"status\": \"aguardando_inicio\",\n        \"texto_auto_save\": \"Texto auto Save\",\n        \"created_at\": null,\n        \"updated_at\": \"2025-02-25T22:08:09.000000Z\"\n    }\n}"}]}, {"name": "Listar 1 formulario( formularioShow )", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/44/formulario/28"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 20:00:30 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "phpdebugbar-id", "value": "X82db1772118a98ea976e75b9b23e90e7"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 22:00:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 28,\n        \"certidao_id\": 44,\n        \"modelo_formulario_id\": 3,\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n        \"texto\": \"TExto 1\",\n        \"versao\": \"3\",\n        \"user_id\": 1,\n        \"created_at\": null,\n        \"updated_at\": \"2025-02-25T19:47:23.000000Z\",\n        \"nome\": \"Formulario Não Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": null\n    }\n}"}, {"name": "Listar 1 formulario( formularioShow )", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:07:02 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:07:02 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 318,\n        \"certidao_id\": 145,\n        \"modelo_formulario_id\": 36,\n        \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n        \"data_preenchimento\": null,\n        \"texto\": \"<h1><PERSON>er<PERSON>cio não analisado</h1><p>Exercício em análise - <strong>2022</strong> o Municí;oio Cumpriu com o disposto nos seguintes artigos da onstituição Federa : 4rt.167, lll e \\\"Art. 167-4\\\", conforme estabelecido no art. 53, § ío, inciso l, da Lei Complementar no 101/2000: Cumpriu com o disposto nos seguintes artigos da Constituição Federal/88: art. 198, com 17,3'lo/o (dezessete vÍrgula trinta e um por cento) da Receita Líquida de lmpostos; art.212, com 25,15% (vinte e cinco vírgula quinze por cento), da Receita Líquida de lmpostos. Cumpriu com o disposto nos seguintes artigos da Lei Complementar no 10í/2000: Art. 11, art.23, art. 33, art. 37, arl. 52 e art. 55, § 2. Receita Corrente Líquida - RCL, no valor de R$<strong> 5.429.502,53</strong> Despesa com Pessoal, Cumpriu, aplicando o valor de R$ 9í.2í2.453,39 (noventa e um milhões, duzentos e doze mil, quatrocentos e cinquenta e três reais e trinta e nove centavos), peíazendo um percentual de 4,500Â (quarenta e quatro vírgula cinquenta por cento). O Poder Executivo, Cumpriu, sendo aplicado o valor de R$ <strong>248.421.000,00</strong>O Poder Legislativo Cumpriu, no v 223.633, (trêsf Certidão Prefeitura de <strong>Jacuípe</strong>/Al - TC-735/2023. alordeR$3. ftu Página 1de 3 vinte reais e quatro centavos), perfazendo um percentual de 42,93o/o (quarenta e dois i:Elt.i iiüra I \\\\:?É,. TCE.AL TRIBUNAL DE CONTAS DO ESTADO DE ALAGOAS milhões, duzentos e vinte e três mil, seiscentos e trinta e três reais e trinta e cinco centavos), correspondendo ao percentual de 1,57% (um vÍ.gula cinquenta e sete por cento)'</p>\",\n        \"versao\": \"1\",\n        \"user_id\": 3192,\n        \"created_at\": null,\n        \"updated_at\": null,\n        \"exercicio\": \"2022\",\n        \"nome\": \"Formulario Nao Analisado\",\n        \"nome_formulario\": \"2022 - Formulario Nao Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"status_descricao\": \"Aguardando início\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": null\n    }\n}"}]}, {"name": "Auto-save certidao ( autoSaveCertidao)", "request": {"method": "PATCH", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"Texto auto Save\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/auto-save"}, "response": [{"name": "Success", "originalRequest": {"method": "PATCH", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"Texto auto Save\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao/44/formulario/28/auto-save"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 22:10:36 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "X13c9961335c4193d1ddb20c53a6fd700"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Wed, 26 Feb 2025 00:10:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": \"Certidão salva Automáticamente!\"\n}"}, {"name": "Auto-save certidao ( autoSaveCertidao)", "originalRequest": {"method": "PATCH", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"Texto auto Save\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/auto-save"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:07:22 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:07:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": \"Certidão salva Automáticamente!\"\n}"}]}, {"name": "Reabrir 1 formulario( formularioReabrir ) (ERRO NO STATUS)", "request": {"method": "PATCH", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/reabrir"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/44/formulario/28"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 20:00:30 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "phpdebugbar-id", "value": "X82db1772118a98ea976e75b9b23e90e7"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 22:00:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 28,\n        \"certidao_id\": 44,\n        \"modelo_formulario_id\": 3,\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_preenchimento\": \"2025-02-25T21:47:34.000000Z\",\n        \"texto\": \"TExto 1\",\n        \"versao\": \"3\",\n        \"user_id\": 1,\n        \"created_at\": null,\n        \"updated_at\": \"2025-02-25T19:47:23.000000Z\",\n        \"nome\": \"Formulario Não Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": null\n    }\n}"}, {"name": "Reabrir 1 formulario( formularioReabrir ) (ERRO NO STATUS)", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:07:54 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:07:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 318,\n        \"certidao_id\": 145,\n        \"modelo_formulario_id\": 36,\n        \"data_criacao\": \"2025-03-08T16:41:59.000000Z\",\n        \"data_preenchimento\": null,\n        \"texto\": \"<h1><PERSON>er<PERSON>cio não analisado</h1><p>Exercício em análise - <strong>2022</strong> o Municí;oio Cumpriu com o disposto nos seguintes artigos da onstituição Federa : 4rt.167, lll e \\\"Art. 167-4\\\", conforme estabelecido no art. 53, § ío, inciso l, da Lei Complementar no 101/2000: Cumpriu com o disposto nos seguintes artigos da Constituição Federal/88: art. 198, com 17,3'lo/o (dezessete vÍrgula trinta e um por cento) da Receita Líquida de lmpostos; art.212, com 25,15% (vinte e cinco vírgula quinze por cento), da Receita Líquida de lmpostos. Cumpriu com o disposto nos seguintes artigos da Lei Complementar no 10í/2000: Art. 11, art.23, art. 33, art. 37, arl. 52 e art. 55, § 2. Receita Corrente Líquida - RCL, no valor de R$<strong> 5.429.502,53</strong> Despesa com Pessoal, Cumpriu, aplicando o valor de R$ 9í.2í2.453,39 (noventa e um milhões, duzentos e doze mil, quatrocentos e cinquenta e três reais e trinta e nove centavos), peíazendo um percentual de 4,500Â (quarenta e quatro vírgula cinquenta por cento). O Poder Executivo, Cumpriu, sendo aplicado o valor de R$ <strong>248.421.000,00</strong>O Poder Legislativo Cumpriu, no v 223.633, (trêsf Certidão Prefeitura de <strong>Jacuípe</strong>/Al - TC-735/2023. alordeR$3. ftu Página 1de 3 vinte reais e quatro centavos), perfazendo um percentual de 42,93o/o (quarenta e dois i:Elt.i iiüra I \\\\:?É,. TCE.AL TRIBUNAL DE CONTAS DO ESTADO DE ALAGOAS milhões, duzentos e vinte e três mil, seiscentos e trinta e três reais e trinta e cinco centavos), correspondendo ao percentual de 1,57% (um vÍ.gula cinquenta e sete por cento)'</p>\",\n        \"versao\": \"1\",\n        \"user_id\": 3192,\n        \"created_at\": null,\n        \"updated_at\": \"2025-03-12T09:07:23.000000Z\",\n        \"exercicio\": \"2022\",\n        \"nome\": \"Formulario Nao Analisado\",\n        \"nome_formulario\": \"2022 - Formulario Nao Analisado\",\n        \"status\": \"aguardando_inicio\",\n        \"status_descricao\": \"Aguardando início\",\n        \"user_finalizado\": null,\n        \"texto_auto_save\": \"Texto auto Save\"\n    }\n}"}]}, {"name": "Get Logs de 1 formulario( formularioLogAcao)", "request": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"acao\": \"finalizar_formulario\",\n    \"valor\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidao/44/formulario/28/log"}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"acao\": \"finalizar_formulario\",\n    \"valor\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao/145formulario/318/log"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 20:36:12 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "Xa30364c635208936562cdd785472deba"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 22:36:13 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"certidao_formulario_id\": 28,\n        \"acao\": \"finalizar_formulario\",\n        \"valor\": \"100\",\n        \"user_id\": 1,\n        \"data_acao\": \"2025-02-25T20:36:13.000000Z\",\n        \"id\": 3,\n        \"acao_texto\": \"Finalizar Formulário\"\n    }\n}"}]}, {"name": "Get Ações de 1 formulario( formularioGetAcoes)", "request": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/acoes"}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao/44/formulario/28/acoes"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 20:24:41 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "X0d21f109a62090b5a462b4d754dbb2fa"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 22:24:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"current_page\": 1,\n    \"data\": [\n        {\n            \"id\": 1,\n            \"certidao_formulario_id\": 28,\n            \"acao\": \"restaurar_versao\",\n            \"valor\": \"1\",\n            \"user_id\": 1,\n            \"data_acao\": \"2025-02-25T19:47:23.000000Z\",\n            \"created_at\": null,\n            \"updated_at\": null,\n            \"acao_texto\": \"Restaurar Versão\",\n            \"user\": {\n                \"id\": 1,\n                \"name\": \"Administra<PERSON>\",\n                \"email\": \"<EMAIL>\",\n                \"email_verified_at\": null,\n                \"created_at\": \"2025-02-17T11:25:20.000000Z\",\n                \"updated_at\": \"2025-02-17T11:25:20.000000Z\",\n                \"type\": 0,\n                \"role\": 0,\n                \"cpf\": \"92641326191\",\n                \"guid\": null,\n                \"domain\": null,\n                \"data_nascimento\": \"01/06/2021\",\n                \"nome_do_pai\": null,\n                \"nome_da_mae\": null,\n                \"rg\": null,\n                \"rg_orgao_expedidor\": null,\n                \"rg_data_expedicao\": null,\n                \"sexo\": null,\n                \"endereco_logradouro\": null,\n                \"endereco_numero\": null,\n                \"endereco_bairro\": null,\n                \"endereco_cep\": null,\n                \"endereco_cidade_id\": null,\n                \"telefones\": null,\n                \"status\": 1,\n                \"roles\": []\n            }\n        }\n    ],\n    \"first_page_url\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/acoes?page=1\",\n    \"from\": 1,\n    \"last_page\": 1,\n    \"last_page_url\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/acoes?page=1\",\n    \"links\": [\n        {\n            \"url\": null,\n            \"label\": \"&laquo; Anterior\",\n            \"active\": false\n        },\n        {\n            \"url\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/acoes?page=1\",\n            \"label\": \"1\",\n            \"active\": true\n        },\n        {\n            \"url\": null,\n            \"label\": \"Próximo &raquo;\",\n            \"active\": false\n        }\n    ],\n    \"next_page_url\": null,\n    \"path\": \"http://localhost:8084/api/certidao-pvl/certidao/44/formulario/28/acoes\",\n    \"per_page\": 15,\n    \"prev_page_url\": null,\n    \"to\": 1,\n    \"total\": 1\n}"}, {"name": "Get Ações de 1 formulario( formularioGetAcoes)", "originalRequest": {"method": "GET", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": "{{base_url}}/certidao-pvl/certidao/145/formulario/318/acoes"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 12 Mar 2025 09:08:17 GMT"}, {"key": "Server", "value": "Apache/2.4.62 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.22"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "57"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=ysWhD51LR9kPN2FENQJOyVEU8o72LGssJj9itZ8o; expires=Wed, 12 Mar 2025 11:08:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": [],\n    \"links\": {\n        \"first\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/acoes?page=1\",\n        \"last\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/acoes?page=1\",\n        \"prev\": null,\n        \"next\": null\n    },\n    \"meta\": {\n        \"current_page\": 1,\n        \"from\": null,\n        \"last_page\": 1,\n        \"links\": [\n            {\n                \"url\": null,\n                \"label\": \"&laquo; Anterior\",\n                \"active\": false\n            },\n            {\n                \"url\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/acoes?page=1\",\n                \"label\": \"1\",\n                \"active\": true\n            },\n            {\n                \"url\": null,\n                \"label\": \"Próximo &raquo;\",\n                \"active\": false\n            }\n        ],\n        \"path\": \"http://localhost:8084/api/certidao-pvl/certidao/145/formulario/318/acoes\",\n        \"per_page\": 15,\n        \"to\": null,\n        \"total\": 0\n    }\n}"}]}, {"name": "<PERSON><PERSON><PERSON> ( criarCertidao )", "request": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cidade_id\": 69\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidao/criar"}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cidade_id\": 69\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao/criar"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 17:02:48 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "Xd35249104c0064d7345e4707f3d9e19f"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 19:02:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 44,\n        \"modelo\": {\n            \"id\": 2,\n            \"nome\": \"Modelo Analise Teste\",\n            \"data_inicio\": \"2023-01-01T03:00:00.000000Z\",\n            \"data_fim\": \"2030-01-01T03:00:00.000000Z\",\n            \"tipo_analise\": \"governo\",\n            \"esfera\": 2,\n            \"diretoria_id\": 1,\n            \"created_at\": \"2025-02-17T14:26:11.000000Z\",\n            \"updated_at\": \"2025-02-17T14:26:11.000000Z\",\n            \"categoria_documento\": 2,\n            \"formularios\": [\n                {\n                    \"id\": 2,\n                    \"nome\": \"Formulario Analisado\",\n                    \"texto\": \"esse texto de exemplo testa a variavel \\\\$_rgf{RGF-Anexo 01_DespesaComPessoalBruta_<MR-10>} outro exemplo \\\\$_rgf{RGF-Anexo 02_DespesaComPessoalBruta_<MR-11>} \\\\$_rgf{RGF-Anexo 03_DespesaComPessoalBruta_<MR-12>}\\n\\nesse texto de exemplo testa a variavel $_rreo{RREO-Anexo 02_RREO2TotalDespesasIntra_DOTAÇÃO INICIAL}\",\n                    \"versao\": 1,\n                    \"status\": \"ativo\",\n                    \"created_at\": \"2025-02-17T14:26:11.000000Z\",\n                    \"updated_at\": \"2025-02-17T14:26:11.000000Z\",\n                    \"capa\": false,\n                    \"arquivo_capa\": null,\n                    \"pivot\": {\n                        \"modelo_analise_id\": 2,\n                        \"modelo_formulario_id\": 2\n                    }\n                }\n            ]\n        },\n        \"cidade\": \"Maceió\",\n        \"responsavel\": \"Administrador\",\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_conclusao\": null,\n        \"assinatura\": null,\n        \"status\": \"aberta\",\n        \"created_at\": \"2025-02-25T17:02:48.000000Z\",\n        \"updated_at\": \"2025-02-25T17:02:48.000000Z\"\n    }\n}"}, {"name": "Fails", "originalRequest": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cidade_id\": 61\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao/criar"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 17:03:17 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "phpdebugbar-id", "value": "X87c63271c8879665d0c23fba25526c03"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 19:03:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"O campo cidade id selecionado é inválido.\",\n    \"errors\": {\n        \"cidade_id\": [\n            \"O campo cidade id selecionado é inválido.\"\n        ]\n    }\n}"}]}, {"name": "<PERSON><PERSON><PERSON> ( emitirCertidao )", "request": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cidade_id\": 69\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao-pvl/certidao/144/emitir"}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cidade_id\": 69\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao/criar"}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 17:02:48 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "phpdebugbar-id", "value": "Xd35249104c0064d7345e4707f3d9e19f"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 19:02:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"data\": {\n        \"id\": 44,\n        \"modelo\": {\n            \"id\": 2,\n            \"nome\": \"Modelo Analise Teste\",\n            \"data_inicio\": \"2023-01-01T03:00:00.000000Z\",\n            \"data_fim\": \"2030-01-01T03:00:00.000000Z\",\n            \"tipo_analise\": \"governo\",\n            \"esfera\": 2,\n            \"diretoria_id\": 1,\n            \"created_at\": \"2025-02-17T14:26:11.000000Z\",\n            \"updated_at\": \"2025-02-17T14:26:11.000000Z\",\n            \"categoria_documento\": 2,\n            \"formularios\": [\n                {\n                    \"id\": 2,\n                    \"nome\": \"Formulario Analisado\",\n                    \"texto\": \"esse texto de exemplo testa a variavel \\\\$_rgf{RGF-Anexo 01_DespesaComPessoalBruta_<MR-10>} outro exemplo \\\\$_rgf{RGF-Anexo 02_DespesaComPessoalBruta_<MR-11>} \\\\$_rgf{RGF-Anexo 03_DespesaComPessoalBruta_<MR-12>}\\n\\nesse texto de exemplo testa a variavel $_rreo{RREO-Anexo 02_RREO2TotalDespesasIntra_DOTAÇÃO INICIAL}\",\n                    \"versao\": 1,\n                    \"status\": \"ativo\",\n                    \"created_at\": \"2025-02-17T14:26:11.000000Z\",\n                    \"updated_at\": \"2025-02-17T14:26:11.000000Z\",\n                    \"capa\": false,\n                    \"arquivo_capa\": null,\n                    \"pivot\": {\n                        \"modelo_analise_id\": 2,\n                        \"modelo_formulario_id\": 2\n                    }\n                }\n            ]\n        },\n        \"cidade\": \"Maceió\",\n        \"responsavel\": \"Administrador\",\n        \"data_criacao\": \"2025-02-25T17:02:48.000000Z\",\n        \"data_conclusao\": null,\n        \"assinatura\": null,\n        \"status\": \"aberta\",\n        \"created_at\": \"2025-02-25T17:02:48.000000Z\",\n        \"updated_at\": \"2025-02-25T17:02:48.000000Z\"\n    }\n}"}, {"name": "Fails", "originalRequest": {"method": "POST", "header": [{"key": "User-Agent", "value": "PostmanRuntime/*******", "type": "text", "disabled": true}, {"key": "X-API-Token", "value": "{{API-TOKEN}}", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"cidade_id\": 61\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/certidao/criar"}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "<PERSON><PERSON>, 25 Feb 2025 17:03:17 GMT"}, {"key": "Server", "value": "Apache/2.4.59 (<PERSON><PERSON>)"}, {"key": "X-Powered-By", "value": "PHP/8.2.13"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "58"}, {"key": "phpdebugbar-id", "value": "X87c63271c8879665d0c23fba25526c03"}, {"key": "Vary", "value": "Origin"}, {"key": "Set-<PERSON><PERSON>", "value": "siap_sistema_integrado_de_auditoria_publica_session=8aaIBN3nbHyxiufrqD2E6i8qE3PE2VyVCEPDrLJ6; expires=Tue, 25 Feb 2025 19:03:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"O campo cidade id selecionado é inválido.\",\n    \"errors\": {\n        \"cidade_id\": [\n            \"O campo cidade id selecionado é inválido.\"\n        ]\n    }\n}"}]}]}