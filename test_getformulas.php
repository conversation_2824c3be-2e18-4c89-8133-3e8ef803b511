<?php

// Teste direto do método getFormulas
$texto = '==#({Impostos, Taxas e Contribuições de Melhoria} >= 1000 ? \'adequado\' : \'inadequado\')#';

echo "Texto: " . $texto . PHP_EOL;

// Testar regex diretamente
$pattern = '/==#(.*?)#/';

if (preg_match_all($pattern, $texto, $matches)) {
    echo "Fórmulas encontradas: " . count($matches[1]) . PHP_EOL;
    foreach ($matches[1] as $formula) {
        echo "Fórmula: " . $formula . PHP_EOL;
    }
} else {
    echo "Nenhuma fórmula encontrada" . PHP_EOL;
}

// Testar extração de variáveis
$formula = '{Impostos, Taxas e Contribuições de Melhoria} >= 1000 ? \'adequado\' : \'inadequado\'';
echo "\nTestando extração de variáveis da fórmula: " . $formula . PHP_EOL;

preg_match_all('/\{([^}]+)\}/', $formula, $matches);
$variaveis = array_unique($matches[1]);

echo "Variáveis encontradas: " . count($variaveis) . PHP_EOL;
foreach($variaveis as $var) {
    echo "Variável: " . $var . PHP_EOL;
}
