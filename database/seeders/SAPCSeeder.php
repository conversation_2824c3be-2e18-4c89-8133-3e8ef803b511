<?php

namespace Database\Seeders;

use Database\Seeders\Sapc\CertidaoCidadeExercicioSeeder;
use Database\Seeders\Sapc\SapcModeloAnaliseSeeder;
use Database\Seeders\Sapc\SapcParametrizacaoSeeder;
use Illuminate\Database\Seeder;

class SAPCSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            SapcParametrizacaoSeeder::class,
            SapcModeloAnaliseSeeder::class,
            CertidaoCidadeExercicioSeeder::class,
            // SapcModeloAnaliseSeeder::class,
        ]);
    }
}
