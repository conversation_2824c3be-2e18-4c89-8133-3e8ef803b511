<?php

namespace Database\Seeders\Sapc;

use App\Enums\Esfera;
use App\Models\Sapc\CidadeExercicioAnalisado;
use App\Models\Cidade;
use Illuminate\Database\Seeder;

class CertidaoCidadeExercicioSeeder extends Seeder
{
    private const ESTADO_MACEIO = 'Maceió';

    public function run()
    {
        $cidadesMunicipais = Cidade::query()->where('nome', "!=", self::ESTADO_MACEIO)->get();
        foreach ($cidadesMunicipais as $cidade) {
            foreach (range(2022, date('Y')) as $exercicio) {
                $registerMunicipal = [
                    'esfera' => Esfera::Municipal,
                    'cidade_id' => $cidade->id,
                    'exercicio' => $exercicio
                ];

                CidadeExercicioAnalisado::firstOrCreate($registerMunicipal, ['analisado' => false]);
            }
        }

        $maceioEstadual = Cidade::query()->where('nome', "=", self::ESTADO_MACEIO)->first();
        if ($maceioEstadual) {
            foreach (range(2018, date('Y')) as $exercicio) {
                $registerMunicipal = [
                    'esfera' => Esfera::Estadual,
                    'cidade_id' => $maceioEstadual->id,
                    'exercicio' => $exercicio
                ];

                CidadeExercicioAnalisado::firstOrCreate($registerMunicipal, ['analisado' => false]);
            }
        }
    }
}
