<?php

namespace Database\Seeders\Sapc;

use App\Models\Sapc\Parametrizacao;
use Illuminate\Database\Seeder;

class SapcParametrizacaoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            [
                'chave' => 'esfera_estadual_qtde_dias_prazo_analise',
                'valor' => 60,
            ],
            [
                'chave' => 'esfera_municipal_qtde_dias_prazo_analise',
                'valor' => 180,
            ],
            [
                'chave' => 'gerar_analise_bimestre',
                'valor' => 20,
            ],
            [
                'chave' => 'cabecalho_padrao',
                'valor' => 'Relatório Técnico sobre as Contas de Governo',
            ],
            [
                'chave' => 'certidao_dias_expiracao', 
                'valor' => '90'            
            ],
        ];

        foreach ($data as $obj) {
            Parametrizacao::updateOrCreate(['chave' => $obj['chave']], $obj);
        }
    }
}
