<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sapc.certidao_pvl_formularios_versao', function (Blueprint $table) {
            $table->id();
            $table->foreignId('certidao_formulario_id')->constrained('sapc.certidao_pvl_formularios')->onDelete('cascade');
            $table->dateTime('data_preenchimento');
            $table->text('texto');
            $table->integer('versao');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sapc.certidao_pvl_formularios_versao');
    }
};
