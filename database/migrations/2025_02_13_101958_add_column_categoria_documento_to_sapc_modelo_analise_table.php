<?php

use App\Enums\Sapc\CategoriaDocumento;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('sapc.modelo_analise', function (Blueprint $table) {
            $table
                ->enum('categoria_documento', [CategoriaDocumento::PRESTACAO_CONTAS_ECONTAS, CategoriaDocumento::CERTIDAO])
                ->default(CategoriaDocumento::PRESTACAO_CONTAS_ECONTAS);
        });
    }

    public function down(): void
    {
        Schema::table('sapc.modelo_analise', function (Blueprint $table) {
            $table->dropColumn('categoria_documento');
        });
    }
};
