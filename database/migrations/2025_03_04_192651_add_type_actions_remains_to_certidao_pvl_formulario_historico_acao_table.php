<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl_formulario_historico_acao DROP CONSTRAINT IF EXISTS certidao_pvl_formulario_historico_acao_acao_check");

        DB::statement("ALTER TABLE sapc.certidao_pvl_formulario_historico_acao ADD CONSTRAINT certidao_pvl_formulario_historico_acao_acao_check CHECK (acao IN ('salvar_formulario', 'finalizar_formulario', 'restaurar_versao', 'reabrir_formulario'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl_formulario_historico_acao DROP CONSTRAINT IF EXISTS certidao_pvl_formulario_historico_acao_acao_check");

        DB::statement("ALTER TABLE sapc.certidao_pvl_formulario_historico_acao ADD CONSTRAINT certidao_pvl_formulario_historico_acao_acao_check CHECK (acao IN ('salvar_formulario', 'finalizar_formulario', 'restaurar_versao'))");
    }
};
