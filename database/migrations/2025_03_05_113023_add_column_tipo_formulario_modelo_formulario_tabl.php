<?php

use App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum;
use App\Models\Certidao;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::table('sapc.modelo_formulario', function (Blueprint $table) {
            $table->enum('tipo_formulario', 
                            [
                                CertidaoPVLTipoModeloFormularioEnum::CONTEUDO, 
                                CertidaoPVLTipoModeloFormularioEnum::MARCACAO,
                                CertidaoPVLTipoModeloFormularioEnum::ANALISADO,
                                CertidaoPVLTipoModeloFormularioEnum::NAO_ANALISADO
                                ])->default(CertidaoPVLTipoModeloFormularioEnum::CONTEUDO);
        });
    }

    /**
     * Reverse the migrations.
    */
    public function down(): void
    {
        Schema::table('sapc.modelo_formulario', function (Blueprint $table) {
            $table->dropColumn('tipo_formulario');
        });
    }
};
