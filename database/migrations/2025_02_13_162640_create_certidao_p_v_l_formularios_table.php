<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sapc.certidao_pvl_formularios', function (Blueprint $table) {
            $table->id();
            $table->foreignId('certidao_id')->constrained('sapc.certidao_pvl');
            $table->foreignId('modelo_formulario_id')->constrainded('sapc.modelo_formulario');
            $table->dateTime('data_criacao');
            $table->dateTime('data_preenchimento')->nullable();
            $table->text('texto');
            $table->string('versao');
            $table->foreignId('user_id')->constrained('users');
            $table->enum('status', [
                'aguardando_inicio',
                'iniciado',
                'finalizado',
            ]);
            $table->text('texto_auto_save')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sapc.certidao_pvl_formularios');
    }
};
