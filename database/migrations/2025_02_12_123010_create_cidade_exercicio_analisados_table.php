<?php

use App\Enums\Esfera;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up() {
        Schema::create('sapc.cidade_exercicio_analisado', function (Blueprint $table) {
            $table->id();
            $table->integer('exercicio');
            $table->boolean('analisado')->default(false);
            $table->enum('esfera', [Esfera::Estadual, Esfera::Municipal]);

            $table->foreignId('cidade_id')
                ->constrained()
                ->onDelete('cascade');

            $table->unique(['exercicio', 'cidade_id', 'esfera']);
            $table->timestamps();
        });
    }

    public function down() {
        Schema::dropIfExists('cidade_exercicio_analisado');
    }
};
