<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("CREATE MATERIALIZED VIEW dados_consolidados.credito_adicional AS 
        SELECT '2022'::text AS exercicio,
    cs.id,
    cs.numero_acao,
    cs.numero_plano_interno,
    cs.codigo_unidade_gestora,
    cs.codigo_unidade_orcamentaria,
    cs.codigo_fonte_recurso_proprio AS codigo_fonte_recurso,
    1 AS tipo_credito,
    cs.tipo AS origem_recurso,
    cs.valor,
    NULL::smallint AS tipo_ato,
    NULL::character varying AS numero_ato,
    NULL::date AS data_publicacao_ato,
    NULL::smallint AS veiculo_publicacao_ato,
    cs.created_at,
    cs.updated_at,
    rp.id AS remessa_parcial_id
   FROM exercicio_2022.sicap_credito_suplementar cs
     JOIN remessa_parcials rp ON cs.remessa_parcial_id = rp.id
UNION ALL
 SELECT '2024'::text AS exercicio,
    ca.id,
    ca.numero_acao,
    ca.numero_plano_interno,
    ca.codigo_unidade_gestora,
    ca.codigo_unidade_orcamentaria::character varying AS codigo_unidade_orcamentaria,
    ca.codigo_fonte_recurso_proprio AS codigo_fonte_recurso,
    ca.tipo_credito,
    ca.origem_recurso,
    ca.valor,
    ca.tipo_ato,
    ca.numero_ato,
    ca.data_publicacao_ato,
    ca.veiculo_publicacao_ato,
    ca.created_at,
    ca.updated_at,
    rp.id AS remessa_parcial_id
   FROM exercicio_2024.sicap_credito_adicional ca
     JOIN remessa_parcials rp ON ca.remessa_parcial_id = rp.id
UNION ALL
 SELECT '2025'::text AS exercicio,
    ca.id,
    ca.numero_acao,
    ca.numero_plano_interno,
    ca.codigo_unidade_gestora,
    ca.codigo_unidade_orcamentaria,
    ca.codigo_fonte_recurso,
    ca.tipo_credito,
    ca.origem_recurso,
    ca.valor,
    ca.tipo_ato,
    ca.numero_ato,
    ca.data_publicacao_ato,
    ca.veiculo_publicacao_ato,
    ca.created_at,
    ca.updated_at,
    rp.id AS remessa_parcial_id
   FROM exercicio_2025.sicap_credito_adicional ca
     JOIN remessa_parcials rp ON ca.remessa_parcial_id = rp.id
WITH DATA;
        ");

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dados_consolidados.credito_adicional');
    }
};
