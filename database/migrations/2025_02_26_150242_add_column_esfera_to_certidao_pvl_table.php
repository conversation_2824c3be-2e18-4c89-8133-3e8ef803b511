<?php

use App\Enums\Esfera;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sapc.certidao_pvl', function (Blueprint $table) {
            $table->enum('esfera', [Esfera::Estadual, Esfera::Municipal])->default(Esfera::Municipal);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sapc.certidao_pvl', function (Blueprint $table) {
            $table->dropColumn('esfera');
        });
    }
};
