<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sapc.certidao_pvl', function (Blueprint $table) {
            $table->string('oficio', 30)->default('');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sapc.certidao_pvl', function (Blueprint $table) {
            $table->dropColumn('oficio');
        });
    }
};
