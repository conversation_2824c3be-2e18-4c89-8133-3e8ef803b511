<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sapc.certidao_pvl', function (Blueprint $table) {
            $table->id();
            $table->foreignId('modelo_analise_id')->constrained('sapc.modelo_analise')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('cidade_id')->constrained('cidades')->onDelete('cascade');
            $table->dateTime('data_criacao');
            $table->dateTime('data_finalizada')->nullable();
            $table->enum('status', ['aberta', 'fechada'])->default('aberta');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sapc.certidao_pvl');
    }
};
