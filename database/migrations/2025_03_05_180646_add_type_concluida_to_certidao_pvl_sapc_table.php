<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl DROP CONSTRAINT IF EXISTS certidao_pvl_status_check");

        DB::statement("ALTER TABLE sapc.certidao_pvl ADD CONSTRAINT certidao_pvl_status_check CHECK (status IN ('aberta', 'iniciada', 'emitida', 'vencida', 'concluida'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl DROP CONSTRAINT IF EXISTS certidao_pvl_status_check");

        DB::statement("ALTER TABLE sapc.certidao_pvl ADD CONSTRAINT certidao_pvl_status_check CHECK (status IN ('aberta', 'iniciada', 'emitida', 'vencida'))");
    }
};
