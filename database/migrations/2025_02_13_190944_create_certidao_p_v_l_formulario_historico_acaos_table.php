<?php

use App\Enums\Sapc\FormularioHistoricoAcao;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sapc.certidao_pvl_formulario_historico_acao', function (Blueprint $table) {
            $table->id();
            $table->foreignId('certidao_formulario_id')
                ->constrained('sapc.certidao_pvl_formularios')
                ->onDelete('cascade');
            $table->enum('acao', [
                FormularioHistoricoAcao::SalvarFormulario,
                FormularioHistoricoAcao::FinalizarFormulario,
                FormularioHistoricoAcao::RestaurarVersao
            ]);
            $table->string('valor');
            $table->foreignId('user_id')
                ->constrained('users');
            $table->dateTime('data_acao');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sapc.certidao_pvl_formulario_historico_acao');
    }
};
