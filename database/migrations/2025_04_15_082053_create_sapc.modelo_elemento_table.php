<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable("sapc.modelo_elemento")) {
            Schema::create("sapc.modelo_elemento", function (Blueprint $table) {
                $table->id();

                $table->string("variavel");
                $table->text('consulta');
                $table->text('coluna');

                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sapc.modelo_elemento');
    }
};
