<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl_formularios DROP CONSTRAINT IF EXISTS certidao_pvl_formularios_status_check");

        DB::statement("ALTER TABLE sapc.certidao_pvl_formularios ADD CONSTRAINT certidao_pvl_formularios_status_check CHECK (status IN ('aguardando_inicio', 'iniciado', 'finalizado', 'reaberto'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl_formularios DROP CONSTRAINT IF EXISTS certidao_pvl_formularios_status_check");

        DB::statement("ALTER TABLE sapc.certidao_pvl_formularios ADD CONSTRAINT certidao_pvl_formularios_status_check CHECK (status IN ('aguardando_inicio', 'iniciado', 'finalizado'))");
    }
};
