<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sapc.certidao_pvl_formularios_versao', function (Blueprint $table) {
            $table->datetime('data_preenchimento')->nullable(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sapc.certidao_pvl_formularios_versao', function (Blueprint $table) {
            $table->datetime('data_preenchimento')->nullable(false)->change();
        });
    }
};
