<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl DROP CONSTRAINT IF EXISTS certidao_pvl_status_check");

        DB::statement("UPDATE sapc.certidao_pvl SET status = 'iniciada' WHERE status = 'fechada'");

        DB::statement("ALTER TABLE sapc.certidao_pvl ADD CONSTRAINT certidao_pvl_status_check CHECK (status IN ('aberta', 'iniciada', 'emitida', 'vencida'))");
    }

    public function down()
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl DROP CONSTRAINT IF EXISTS certidao_pvl_status_check");

        DB::statement("UPDATE sapc.certidao_pvl SET status = 'fechada' WHERE status = 'iniciada'");

        DB::statement("ALTER TABLE sapc.certidao_pvl ADD CONSTRAINT certidao_pvl_status_check CHECK (status IN ('aberta', 'fechada'))");
    }
};
