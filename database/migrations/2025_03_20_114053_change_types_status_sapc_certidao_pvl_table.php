<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl DROP CONSTRAINT IF EXISTS certidao_pvl_status_check");

        DB::statement("
            UPDATE sapc.certidao_pvl
            SET status = 'em_andamento'
            WHERE status = 'iniciada'
        ");

        DB::statement("
            UPDATE sapc.certidao_pvl
            SET status = 'iniciada'
            WHERE status = 'aberta'
        ");

        DB::statement("ALTER TABLE sapc.certidao_pvl ADD CONSTRAINT certidao_pvl_status_check CHECK (status IN ('iniciada', 'em_andamento', 'emitida', 'vencida', 'concluida'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE sapc.certidao_pvl DROP CONSTRAINT IF EXISTS certidao_pvl_status_check");

        DB::statement("
            UPDATE sapc.certidao_pvl
            SET status = 'aberta'
            WHERE status = 'iniciada'
        ");

        DB::statement("
            UPDATE sapc.certidao_pvl
            SET status = 'iniciada'
            WHERE status = 'em_andamento'
        ");

        DB::statement("ALTER TABLE sapc.certidao_pvl ADD CONSTRAINT certidao_pvl_status_check CHECK (status IN ('aberta', 'iniciada', 'emitida', 'vencida'))");
    }
};
