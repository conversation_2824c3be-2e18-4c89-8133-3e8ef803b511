<?php

namespace Tests\Unit\Sapc;

use App\Models\Sapc\Analise;
use App\Traits\SapcParseText;
use PHPUnit\Framework\TestCase;

class FormulaVariavelCertdTest extends TestCase
{
    use SapcParseText;

    protected $variaveis = [];

    /** @test */
    public function it_calls_replaceFormulas_method_and_processes_formula_without_variables()
    {
        // Criar uma análise de teste
        $analise = new Analise();
        $analise->id = 999;
        $analise->unidade_gestora_id = 1717;
        $analise->exercicio = 2024;

        // Texto com fórmula simples sem variáveis (para não depender do banco)
        $textoComFormula = 'Resultado: ==#(25.74 >= 15.0 ? \'conformidade\':\'desconformidade\')#';

        // Chamar o método replaceFormulas diretamente
        $resultado = $this->replaceFormulas($textoComFormula, $analise);

        // Verificar que a fórmula foi processada
        $this->assertStringNotContainsString('==#', $resultado);
        $this->assertEquals('Resultado: conformidade', $resultado);
    }





    /** @test */
    public function it_preserves_text_without_formulas()
    {
        $analise = new Analise();
        $analise->id = 995;
        $analise->unidade_gestora_id = 1717;
        $analise->exercicio = 2024;

        // Texto sem fórmulas
        $textoSemFormulas = 'Este é um texto normal sem fórmulas para processar.';

        // Chamar replaceFormulas
        $resultado = $this->replaceFormulas($textoSemFormulas, $analise);

        // Texto deve permanecer inalterado
        $this->assertEquals($textoSemFormulas, $resultado);
    }

    // Métodos auxiliares para simular dependências do trait
    protected function buscarMatriz($unidadeGestoraId, $exercicio, $variavelGoverno = null)
    {
        // Simular matriz vazia (sem dados)
        return [];
    }

    protected function calcularVariavel($variavelGoverno, $matriz)
    {
        // Simular cálculo que retorna 0 quando não há dados na matriz
        if (empty($matriz)) {
            return 0;
        }

        // Para testes, retornar um valor fixo
        return 25.74;
    }
}