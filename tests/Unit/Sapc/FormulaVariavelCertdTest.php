<?php

namespace Tests\Unit\Sapc;

use PHPUnit\Framework\TestCase;

class FormulaVariavelCertdTest extends TestCase
{

    /** @test */
    public function it_extracts_certd_variable_name_correctly()
    {
        // Testar extração da variável específica da tarefa
        $formula = '{2_PERCENTUAL DA RECEITA  DE IMPOSTOS E TRANSFERÊNCIAS CONSTITUCIONAIS E LEGAIS APLICADO EM ASPS  (XVI / III)*100 (mínimo de 15% conforme LC n° 141/2012 ou % da Lei Orgânica Municipal)} >= 15,0 ? \'conformidade\':\'desconformidade\'';

        preg_match_all('/\{([^}]+)\}/', $formula, $matches);
        $variaveis = array_unique($matches[1]);

        $this->assertCount(1, $variaveis);
        $this->assertEquals('2_PERCENTUAL DA RECEITA  DE IMPOSTOS E TRANSFERÊNCIAS CONSTITUCIONAIS E LEGAIS APLICADO EM ASPS  (XVI / III)*100 (mínimo de 15% conforme LC n° 141/2012 ou % da Lei Orgânica Municipal)', $variaveis[0]);
    }

    /** @test */
    public function it_demonstrates_problem_before_fix()
    {
        // Este teste demonstra o problema original descrito na tarefa
        // Antes da correção, o resultado seria:
        // "==#(25,74 >= 15,0 ? 'conformidade':'desconformidade')#"
        // Após a correção, deve ser: "conformidade"

        $this->assertTrue(true, 'Este teste documenta o problema original da tarefa CERTD');

        // Problema original:
        // Input:  ==#({2_PERCENTUAL...} >= 15,0 ? 'conformidade':'desconformidade')#
        // Output: ==#(25,74 >= 15,0 ? 'conformidade':'desconformidade')#

        // Solução implementada:
        // Input:  ==#({2_PERCENTUAL...} >= 15,0 ? 'conformidade':'desconformidade')#
        // Output: conformidade
    }
}
