<?php

namespace Tests\Unit\Services;

use App\Enums\Esfera;
use App\Enums\PapelUsuario;
use App\Enums\Sapc\CategoriaDocumento;
use App\Enums\Sapc\CertidaoFormularioHistoricoAcao;
use App\Enums\Sapc\CertidaoFormularioStatus;
use App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum;
use App\Enums\Sapc\CertidaoStatusEnum;
use App\Enums\Sapc\StatusModeloFormulario;
use App\Enums\Sapc\TipoAnalise;
use App\Enums\SubtipoUnidade;
use App\Enums\TipoUnidade;
use App\Enums\TipoUsuario;
use App\Models\Cidade;
use App\Models\Diretoria;
use App\Models\Sapc\CertidaoPVL;
use App\Models\Sapc\CertidaoPVLFormulario;
use App\Models\Sapc\CertidaoPVLFormularioVersao;
use App\Models\Sapc\CidadeExercicioAnalisado;
use App\Models\Sapc\ModeloAnalise;
use App\Models\Sapc\ModeloAnaliseFormulario;
use App\Models\Sapc\ModeloFormulario;
use App\Models\SiconfiRGF;
use App\Models\SiconfiRREO;
use App\Models\UnidadeGestora;
use App\Models\User;
use App\Services\CertidaoPVLService;
use Illuminate\Support\Facades\Auth;
use Mockery;
use Spatie\Browsershot\Browsershot;
use Tests\TestCase;

class CertidaoPVLServiceTest extends TestCase
{
    protected $service;

    protected $authUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CertidaoPVLService;

        $this->authUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => PapelUsuario::Auditor,
            'type' => TipoUsuario::Interno,
        ]);

        Auth::shouldReceive('user')->andReturn($this->authUser);
        Auth::shouldReceive('id')->andReturn($this->authUser->id);
    }

    public function test_criar_certidao()
    {
        $diretoria = Diretoria::factory()->create();

        $mockSiconfiRREO = Mockery::mock('alias:'.\App\Models\SiconfiRREO::class);
        $mockSiconfiRREO->shouldReceive('where')
            ->with([
                'periodo' => '2',
                'anexo' => 'RREO-Anexo 01',
                'cod_conta' => 'ReceitasCorrentes',
                'coluna' => 'PREVISÃO INICIAL',
                'exercicio' => '2022',
            ])
            ->withAnyArgs()
            ->andReturnSelf();

        $mockSiconfiRREO->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '100.50']);

        $mockBuilder = Mockery::mock(\Illuminate\Database\Eloquent\Builder::class);

        $mockBuilder->shouldReceive('where')
            ->withAnyArgs()
            ->andReturnSelf();

        $mockBuilder->shouldReceive('get')
            ->andReturn(collect([
                (object) [
                    'valor' => 100,
                    'variavel' => '$_variavel_alguma_coisa',
                ],
            ]));

        $mockSiconfiRREO->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $mockSiconfiRGF = Mockery::mock('alias:'.\App\Models\SiconfiRGF::class);
        $mockSiconfiRGF->shouldReceive('where')
            ->with([
                'exercicio' => '2024',
                'cod_ibge' => '2704302',
                'periodo' => '2',
                'co_poder' => 'E',
                'anexo' => 'RGF-Anexo 01',
                'cod_conta' => 'ObrigacoesPatronais',
                'coluna' => 'TOTAL (ÚLTIMOS 12 MESES) (a)',
            ])
            ->withAnyArgs()
            ->andReturnSelf();

        $mockSiconfiRGF->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '200.44']);

        $mockSiconfiRGF->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $tiposFormularios = [
            CertidaoPVLTipoModeloFormularioEnum::ANALISADO,
            CertidaoPVLTipoModeloFormularioEnum::NAO_ANALISADO,
            CertidaoPVLTipoModeloFormularioEnum::MARCACAO,
            CertidaoPVLTipoModeloFormularioEnum::CONTEUDO,
        ];

        $modelosFormulario = collect($tiposFormularios)->map(fn ($tipoFormulario) => ModeloFormulario::create([
            'nome' => "Form de teste {$tipoFormulario}",
            'texto' => 'Análise - $_al{Responsáveis Técnicos} $_al_Atual{Exercício}
                        $_al{Controlador} $_al{Unidade Gestora} $_al{Município} $_al{Modelo Análise}  $_al{Data de criação}
                        $_al{Data de conclusão} $_al{Status} $_al{Gestor}
                        $_al_Atual{Exercício} $_rgf{E_2_RGF-Anexo 01_ObrigacoesPatronais_TOTAL (ÚLTIMOS 12 MESES) (a)}
                        $_rreo{2_RREO-Anexo 01_ReceitasCorrentes_PREVISÃO INICIAL}
                        ==#(receita_previsao_atualizada-receita_realizada)#
                        ',
            'status' => StatusModeloFormulario::Ativo,
            'versao' => 1,
            'tipo_formulario' => $tipoFormulario,
        ]));

        foreach ($modelosFormulario as $key => $formulario) {
            ModeloAnaliseFormulario::create([
                'modelo_analise_id' => $modeloAnalise->id,
                'modelo_formulario_id' => $formulario->id,
                'data_inicio' => now()->addYears(-1),
                'data_fim' => now()->addYears(1),
                'ordenacao' => $key,
            ]);
        }

        $cidade = Cidade::factory()->create(['id' => 999, 'nome' => 'Maceió', 'codigo_ibge' => '2704302']);
        $exerciciosAnalisados = collect(range(2022, 2024))->map(fn ($exercicio) => [
            'esfera' => Esfera::Municipal,
            'cidade_id' => $cidade->id,
            'exercicio' => $exercicio,
            'analisado' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        CidadeExercicioAnalisado::insert($exerciciosAnalisados->toArray());

        $unidade = UnidadeGestora::factory()->create([
            'cidade_id' => $cidade->id,
            'tipo_unidade' => TipoUnidade::Prefeitura,
            'subtipo' => SubtipoUnidade::Prefeitura,
            'esfera' => Esfera::Municipal,
        ]);

        $responsavel = User::factory()->create();
        $unidade->users()->attach($responsavel->id, ['role' => PapelUsuario::ResponsavelTecnico]);
        $unidade->diretoria()->associate($diretoria)->save();

        collect(range(2022, 2024))->each(function ($exercicio) use ($cidade) {
            CidadeExercicioAnalisado::updateOrCreate([
                'exercicio' => $exercicio,
                'cidade_id' => $cidade->id,
                'esfera' => Esfera::Municipal,
            ], [
                'analisado' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        });

        $certidao = $this->service->criarCertidao(['ug_id' => $unidade->id]);

        $this->assertInstanceOf(
            CertidaoPVL::class,
            $certidao,
            'Falha: A instância retornada não é do tipo CertidaoPVL.'
        );

        $this->assertEquals(
            $cidade->id,
            $certidao->cidade_id,
            'Falha: A cidade da certidão não é a esperada.'
        );

        $this->assertEquals(
            CertidaoStatusEnum::INICIADA,
            $certidao->status,
            'Falha: O status da certidão não é INICIADA como esperado.'
        );

        $this->assertEquals(
            $this->authUser->id,
            $certidao->user_id,
            'Falha: O user_id da certidão não corresponde ao usuário autenticado.'
        );

        $this->assertTrue(
            $certidao->formularios->every(
                fn ($formulario) => in_array(
                    $formulario->modeloFormulario->tipo_formulario->value,
                    $tiposFormularios,
                    true
                )
            ),
            'Falha: Um ou mais formulários não pertencem aos tipos esperados: '.implode(', ', $tiposFormularios).'.'
        );

        $this->assertTrue(
            $certidao->formularios->every(fn ($formulario) => $formulario->versao == 1),
            'Falha: Nem todos os formulários possuem a versão 1.'
        );

        $listFormulariosCertidao = $this->service->listarCertidaoFormularios($certidao->id);

        $this->assertCount(
            4,
            $listFormulariosCertidao,
            'Falha: A certidão não contém exatamente 4 formulários.'
        );
    }

    public function test_estadual_criar_certidao()
    {
        $mockSiconfiRREO = Mockery::mock('alias:'.\App\Models\SiconfiRREO::class);
        $mockSiconfiRREO->shouldReceive('where')
            ->with([
                'periodo' => '2',
                'anexo' => 'RREO-Anexo 01',
                'cod_conta' => 'ReceitasCorrentes',
                'coluna' => 'PREVISÃO INICIAL',
                'exercicio' => '2022',
            ])
            ->withAnyArgs()
            ->andReturnSelf();

        $mockSiconfiRREO->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '100']);

        $mockSiconfiRREO->shouldReceive('value')
            ->with('valor')
            ->andReturn('100.50');

        $mockBuilder = Mockery::mock(\Illuminate\Database\Eloquent\Builder::class);

        $mockBuilder->shouldReceive('where')
            ->withAnyArgs()
            ->andReturnSelf();

        $mockBuilder->shouldReceive('get')
            ->andReturn(collect([
                (object) [
                    'valor' => 100,
                    'variavel' => '$_variavel_alguma_coisa',
                ],
            ]));

        $mockSiconfiRREO->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $mockSiconfiRGF = Mockery::mock('alias:'.\App\Models\SiconfiRGF::class);
        $mockSiconfiRGF->shouldReceive('where')
            ->with([
                'exercicio' => '2024',
                'cod_ibge' => '2704302',
                'periodo' => '2',
                'co_poder' => 'E',
                'anexo' => 'RGF-Anexo 01',
                'cod_conta' => 'ObrigacoesPatronais',
                'coluna' => 'TOTAL (ÚLTIMOS 12 MESES) (a)',
            ])
            ->withAnyArgs()
            ->andReturnSelf();

        $mockSiconfiRGF->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '100']);

        $mockSiconfiRGF->shouldReceive('value')
            ->with('valor')
            ->andReturn('200.44');

        $mockSiconfiRGF->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Estadual,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $tiposFormularios = [
            CertidaoPVLTipoModeloFormularioEnum::ANALISADO,
            CertidaoPVLTipoModeloFormularioEnum::NAO_ANALISADO,
            CertidaoPVLTipoModeloFormularioEnum::MARCACAO,
            CertidaoPVLTipoModeloFormularioEnum::CONTEUDO,
        ];

        $modelosFormulario = collect($tiposFormularios)->map(fn ($tipoFormulario) => ModeloFormulario::create([
            'nome' => "Form de teste {$tipoFormulario}",
            'texto' => 'Análise - $_al{Responsáveis Técnicos} $_al_Atual{Exercício}
                        ==#(receita_previsao_atualizada-receita_realizada)#
                        $_al{Controlador} $_al{Unidade Gestora} $_al{Município} $_al{Modelo Análise}  $_al{Data de criação}
                        $_al{Data de conclusão} $_al{Status} $_al{Gestor}
                        $_al_Atual{Exercício} $_rgf{E_2_RGF-Anexo 01_ObrigacoesPatronais_TOTAL (ÚLTIMOS 12 MESES) (a)}
                        $_rreo{2_RREO-Anexo 01_ReceitasCorrentes_PREVISÃO INICIAL}',
            'status' => StatusModeloFormulario::Ativo,
            'versao' => 1,
            'tipo_formulario' => $tipoFormulario,
        ]));

        foreach ($modelosFormulario as $key => $formulario) {
            ModeloAnaliseFormulario::create([
                'modelo_analise_id' => $modeloAnalise->id,
                'modelo_formulario_id' => $formulario->id,
                'data_inicio' => now()->addYears(-1),
                'data_fim' => now()->addYears(1),
                'ordenacao' => $key,
            ]);
        }

        $cidade = Cidade::factory()->create(['nome' => 'Maceió', 'id' => 999]);
        $exerciciosAnalisados = collect(range(2018, 2024))->map(fn ($exercicio) => [
            'esfera' => Esfera::Estadual,
            'cidade_id' => $cidade->id,
            'exercicio' => $exercicio,
            'analisado' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        CidadeExercicioAnalisado::insert($exerciciosAnalisados->toArray());

        $unidade = UnidadeGestora::factory()->create([
            'cidade_id' => $cidade->id,
            'tipo_unidade' => TipoUnidade::GovernoDoEstado,
            'subtipo' => SubtipoUnidade::GovernoDoEstado,
            'esfera' => Esfera::Estadual,
        ]);

        $responsavel = User::factory()->create();
        $unidade->users()->attach($responsavel->id, ['role' => PapelUsuario::ResponsavelTecnico]);
        $unidade->diretoria()->associate($diretoria)->save();

        collect(range(2018, 2024))->each(function ($exercicio) use ($cidade) {
            CidadeExercicioAnalisado::updateOrCreate([
                'exercicio' => $exercicio,
                'cidade_id' => $cidade->id,
                'esfera' => Esfera::Estadual,
            ], [
                'analisado' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        });

        $certidao = $this->service->criarCertidao(['ug_id' => $unidade->id]);

        $mockService = Mockery::mock(CertidaoPVLService::class)->makePartial();

        $mockService->shouldReceive('parseTexto')
            ->withAnyArgs()
            ->andReturnUsing(fn ($certidao, $texto, $exercicio, $cidade) => $texto);

        $this->assertInstanceOf(
            CertidaoPVL::class,
            $certidao,
            'Falha: A instância retornada não é do tipo CertidaoPVL.'
        );

        $this->assertEquals(
            $cidade->id,
            $certidao->cidade_id,
            'Falha: A cidade da certidão não é a esperada.'
        );

        $this->assertEquals(
            CertidaoStatusEnum::INICIADA,
            $certidao->status,
            'Falha: O status da certidão não é INICIADA como esperado.'
        );

        $this->assertEquals(
            $this->authUser->id,
            $certidao->user_id,
            'Falha: O user_id da certidão não corresponde ao usuário autenticado.'
        );

        $this->assertTrue(
            $certidao->formularios->every(
                fn ($formulario) => in_array(
                    $formulario->modeloFormulario->tipo_formulario->value,
                    $tiposFormularios,
                    true
                )
            ),
            'Falha: Um ou mais formulários não pertencem aos tipos esperados: '.implode(', ', $tiposFormularios).'.'
        );

        $this->assertTrue(
            $certidao->formularios->every(fn ($formulario) => $formulario->versao == 1),
            'Falha: Nem todos os formulários possuem a versão 1.'
        );

        $this->assertTrue(
            $certidao->esfera->is(Esfera::Estadual),
            'Falha: A esfera de certidao não é Estadual.'
        );

        $listFormulariosCertidao = $this->service->listarCertidaoFormularios($certidao->id);

        $this->assertCount(
            7,
            $listFormulariosCertidao,
            'Falha: A certidão não contém exatamente 7 formulários.'
        );
    }

    public function test_recalcular_certidao()
    {
        Auth::shouldReceive('id')
            ->andReturn($this->authUser->id);

        $cidade = Cidade::factory()->create(['id' => 999]);
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $certidao = CertidaoPVL::create([
            'status' => CertidaoStatusEnum::INICIADA,
            'data_criacao' => now(),
            'data_finalizada' => null,
            'esfera' => Esfera::Municipal,
            'modelo_analise_id' => $modeloAnalise->id,
            'user_id' => auth()->user()->id,
            'cidade_id' => $cidade->id,
        ]);

        $modeloFormulario1 = ModeloFormulario::create([
            'nome' => 'Form de teste',
            'texto' => '==VAR_GOV_1== ==VAR_RREO_1== ==#VAR_GOV_1 + VAR_RREO_1#',
            'status' => StatusModeloFormulario::Ativo,
            'versao' => 1,
            'tipo_formulario' => CertidaoPVLTipoModeloFormularioEnum::CONTEUDO,
        ]);

        $formularioParaRecalculo = CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => $modeloFormulario1->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
            'texto' => 'Texto antigo do formulário',
            'data_criacao' => now(),
            'versao' => 1,
            'user_id' => $this->authUser->id,
        ]);

        $mockService = Mockery::mock(CertidaoPVLService::class)->makePartial();

        $mockService->shouldReceive('parseTexto')
            ->withAnyArgs()
            ->andReturnUsing(function ($certidao, $texto, $exercicio, $cidade) {
                return str_replace(
                    ['==VAR_GOV_1==', '==VAR_RREO_1==', '==#VAR_GOV_1 + VAR_RREO_1#'],
                    ['200', '300', '500'],
                    $texto
                );
            });

        $result = $mockService->recalcularCertidao($certidao);

        $this->assertCount(1, $result, 'Falha: O número de formulários recalculados está incorreto.');

        $this->assertEquals(
            '200 300 500',
            $result[0]['texto'],
            'Falha: O texto do formulário recalculado não foi atualizado corretamente.'
        );

        $this->assertEquals(
            2,
            $result[0]['versao'],
            'Falha: A versão do formulário recalculado não foi incrementada corretamente.'
        );

        $novaVersao = CertidaoPVLFormularioVersao::where('certidao_formulario_id', $formularioParaRecalculo->id)
            ->latest('created_at')
            ->first();

        $this->assertNotNull($novaVersao, 'Falha: Nenhuma nova versão foi registrada para o formulário.');
        $this->assertEquals(1, $novaVersao->versao, 'Falha: A versão armazenada não corresponde à esperada.');
        $this->assertEquals('Texto antigo do formulário', $novaVersao->texto, 'Falha: O texto da versão antiga não foi armazenado corretamente.');

        $certidaoAtualizada = CertidaoPVL::find($certidao->id);

        $this->assertEquals(
            CertidaoStatusEnum::EM_ANDAMENTO,
            $certidaoAtualizada->status,
            'Falha: O status da certidão não foi atualizado corretamente.'
        );
    }

    public function test_nao_recalcular_certidao_quando_nada_muda()
    {
        $cidade = Cidade::factory()->create(['id' => 999]);

        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $certidao = CertidaoPVL::create([
            'status' => CertidaoStatusEnum::VENCIDA,
            'data_criacao' => now(),
            'data_finalizada' => null,
            'esfera' => Esfera::Municipal,
            'modelo_analise_id' => $modeloAnalise->id,
            'user_id' => auth()->user()->id,
            'cidade_id' => $cidade->id,
        ]);

        $modeloFormulario1 = ModeloFormulario::create([
            'nome' => 'Form de teste',
            'texto' => 'Texto modelo atualizado',
            'status' => StatusModeloFormulario::Ativo,
            'versao' => 1,
            'tipo_formulario' => CertidaoPVLTipoModeloFormularioEnum::CONTEUDO,
        ]);

        $formularioIgnorado = CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => $modeloFormulario1->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
            'texto' => 'Texto modelo atualizado',
            'data_criacao' => now(),
            'versao' => 1,
            'user_id' => $this->authUser->id,
        ]);

        Auth::shouldReceive('id')->andReturn($this->authUser->id);

        $mockService = Mockery::mock(CertidaoPVLService::class)->makePartial();

        $mockService->shouldReceive('parseTexto')
            ->withAnyArgs()
            ->andReturnUsing(fn ($certidao, $texto, $exercicio, $cidade) => $texto);

        $mockService->shouldReceive('storeCertidaoPVL')->never();

        $result = $mockService->recalcularCertidao($certidao);

        $this->assertCount(0, $result, 'Falha: Nenhum formulário deveria ser recalculado.');

        $this->assertEquals(
            1,
            $formularioIgnorado->fresh()->versao,
            'Falha: O formulário que não precisava de recalculo teve a versão alterada incorretamente.'
        );

        $this->assertDatabaseMissing('sapc.certidao_pvl_formularios_versao', [
            'certidao_formulario_id' => $formularioIgnorado->id,
        ]);

        $certidaoAtualizada = CertidaoPVL::find($certidao->id);

        $this->assertEquals(
            CertidaoStatusEnum::VENCIDA,
            $certidaoAtualizada->status,
            'Falha: O status da certidão foi alterado indevidamente.'
        );
    }

    public function test_calculo_variavel_rreo()
    {
        $exercicio = 2023;
        $codIbge = '2704302';

        $variavel = (object) [
            'periodo' => 2,
            'anexo' => 'A1',
            'cod_conta' => '123',
            'coluna' => '5',
            'is_extenso' => false,
        ];

        $mockSiconfiRREO = Mockery::mock('alias:'.\App\Models\SiconfiRREO::class);
        $mockSiconfiRREO->shouldReceive('where')
            ->with([
                'exercicio' => $exercicio,
                'cod_ibge' => $codIbge,
                'periodo' => $variavel->periodo,
                'anexo' => $variavel->anexo,
                'cod_conta' => $variavel->cod_conta,
                'coluna' => $variavel->coluna,
            ])
            ->andReturnSelf();

        $mockSiconfiRREO->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '351.50']);

        $mockBuilder = Mockery::mock(\Illuminate\Database\Eloquent\Builder::class);

        $mockBuilder->shouldReceive('where')
            ->withAnyArgs()
            ->andReturnSelf();

        $mockBuilder->shouldReceive('get')
            ->andReturn(collect([
                (object) [
                    'valor' => 100,
                    'variavel' => '$_variavel_alguma_coisa',
                ],
            ]));

        $mockSiconfiRREO->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $service = new CertidaoPVLService;

        $resultado = $service->calcularVariavelRREO($exercicio, $codIbge, $variavel);

        $this->assertEquals(
            '351.50',
            $resultado,
            'Falha: A soma dos valores mockados de RREO não foi calculada corretamente.'
        );
    }

    public function test_calculo_variavel_rgf()
    {
        $exercicio = 2023;
        $codIbge = '2704302';

        $variavel = (object) [
            'co_poder' => 'E',
            'poder' => 'E',
            'periodo' => 2,
            'anexo' => 'B2',
            'cod_conta' => '456',
            'coluna' => '10',
            'is_extenso' => false,
        ];

        $mockSiconfiRGF = Mockery::mock('alias:'.\App\Models\SiconfiRGF::class);

        $mockSiconfiRGF->shouldReceive('where')
            ->with([
                'exercicio' => $exercicio,
                'cod_ibge' => $codIbge,
                'periodo' => $variavel->periodo,
                'co_poder' => $variavel->poder,
                'anexo' => $variavel->anexo,
                'cod_conta' => $variavel->cod_conta,
                'coluna' => $variavel->coluna,
            ])
            ->andReturnSelf();

        $mockSiconfiRGF->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '900.00']);

        $mockBuilder = Mockery::mock(\Illuminate\Database\Eloquent\Builder::class);

        $mockBuilder->shouldReceive('where')
            ->withAnyArgs()
            ->andReturnSelf();

        $mockBuilder->shouldReceive('get')
            ->andReturn(collect([
                (object) [
                    'valor' => 100,
                    'variavel' => '$_variavel_alguma_coisa',
                ],
            ]));

        $mockSiconfiRGF->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $service = new \App\Services\CertidaoPVLService;

        $resultado = $service->calcularVariavelRGF($exercicio, $codIbge, $variavel);

        $this->assertEquals(
            '900.00',
            $resultado,
            'Falha: O valor retornado da variável RGF não corresponde ao esperado.'
        );
    }

    public function test_auto_save_certidao()
    {
        $cidade = Cidade::factory()->create(['id' => 999]);
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $certidao = CertidaoPVL::create([
            'status' => CertidaoStatusEnum::INICIADA,
            'data_criacao' => now(),
            'data_finalizada' => null,
            'esfera' => Esfera::Municipal,
            'modelo_analise_id' => $modeloAnalise->id,
            'user_id' => auth()->user()->id,
            'cidade_id' => $cidade->id,
        ]);

        $modeloFormulario = ModeloFormulario::create([
            'nome' => 'Form de teste',
            'texto' => 'Texto modelo inicial',
            'status' => StatusModeloFormulario::Ativo,
            'versao' => 1,
            'tipo_formulario' => CertidaoPVLTipoModeloFormularioEnum::CONTEUDO,
        ]);

        $formularioCertidao = CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => $modeloFormulario->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
            'texto' => 'Texto inicial do formulário',
            'texto_auto_save' => null,
            'data_criacao' => now(),
            'versao' => 1,
            'user_id' => $this->authUser->id,
        ]);

        $mockService = Mockery::mock(CertidaoPVLService::class)->makePartial();

        $novoTexto = 'Este é um texto salvo automaticamente pelo AutoSave.';

        $mockService->autoSaveCertidao($formularioCertidao, $novoTexto);

        $this->assertEquals(
            $novoTexto,
            $formularioCertidao->fresh()->texto_auto_save,
            'Falha: O texto do AutoSave não foi salvo corretamente no formulário.'
        );
    }

    public function test_get_vars_rreo()
    {
        $texto = 'O total arrecadado foi $_rreo{2_A1_123_5} e $_rreo{2_A2_456_10}.';

        $mockSiconfiRREO = Mockery::mock('alias:'.SiconfiRREO::class);
        $mockSiconfiRREO->shouldReceive('where')
            ->with([
                'periodo' => 2,
                'anexo' => 'A1',
                'coluna' => '5',
                'cod_conta' => '123',
            ])
            ->andReturnSelf();

        $mockSiconfiRREO->shouldReceive('first')
            ->andReturn((object) ['id' => 1, 'periodo' => 2, 'anexo' => 'A1', 'coluna' => '5', 'cod_conta' => '123']);

        $mockSiconfiRREO->shouldReceive('where')
            ->with([
                'periodo' => 2,
                'anexo' => 'A2',
                'coluna' => '10',
                'cod_conta' => '456',
            ])
            ->andReturnSelf();

        $mockSiconfiRREO->shouldReceive('first')
            ->andReturn((object) ['id' => 2, 'periodo' => 2, 'anexo' => 'A2', 'coluna' => '10', 'cod_conta' => '456']);

        $service = new CertidaoPVLService;

        $resultado = $service->getVarsRREO($texto);

        $this->assertCount(2, $resultado, 'Falha: Número de variáveis de RREO encontradas está incorreto.');

        $this->assertEquals('2_A1_123_5', $resultado[0]->variavel, 'Falha: Variável RREO 1 não extraída corretamente.');
        $this->assertEquals('A1', $resultado[0]->anexo);
        $this->assertEquals('123', $resultado[0]->cod_conta);
        $this->assertEquals('5', $resultado[0]->coluna);
        $this->assertEquals('$_rreo{2_A1_123_5}', $resultado[0]->currentExpression);

        $this->assertEquals('2_A2_456_10', $resultado[1]->variavel, 'Falha: Variável RREO 2 não extraída corretamente.');
        $this->assertEquals('A2', $resultado[1]->anexo);
        $this->assertEquals('456', $resultado[1]->cod_conta);
        $this->assertEquals('10', $resultado[1]->coluna);
        $this->assertEquals('$_rreo{2_A2_456_10}', $resultado[1]->currentExpression);
    }

    public function test_get_vars_rgf()
    {
        $texto = 'Os valores do governo foram $_rgf{E_2_B1_789_15} e $_rgf{E_2_B2_654_20}.';

        $mockSiconfiRGF = Mockery::mock('alias:'.SiconfiRGF::class);

        $mockSiconfiRGF->shouldReceive('where')
            ->with([
                'co_poder' => 'E',
                'periodo' => 2,
                'anexo' => 'B1',
                'coluna' => '15',
                'cod_conta' => '789',
            ])
            ->andReturnSelf();

        $mockSiconfiRGF->shouldReceive('first')
            ->andReturn((object) ['id' => 1, 'poder' => 'E', 'periodo' => 2, 'anexo' => 'B1', 'coluna' => '15', 'cod_conta' => '789']);

        $mockSiconfiRGF->shouldReceive('where')
            ->with([
                'co_poder' => 'E',
                'periodo' => 2,
                'anexo' => 'B2',
                'coluna' => '20',
                'cod_conta' => '654',
            ])
            ->andReturnSelf();

        $mockSiconfiRGF->shouldReceive('first')
            ->andReturn((object) ['id' => 2, 'poder' => 'E', 'periodo' => 2, 'anexo' => 'B2', 'coluna' => '20', 'cod_conta' => '654']);

        $service = new CertidaoPVLService;

        $resultado = $service->getVarsRGF($texto);

        $this->assertCount(2, $resultado, 'Falha: Número de variáveis de RGF encontradas está incorreto.');

        $this->assertEquals('E_2_B1_789_15', $resultado[0]->variavel, 'Falha: Variável RGF 1 não extraída corretamente.');
        $this->assertEquals('B1', $resultado[0]->anexo);
        $this->assertEquals('789', $resultado[0]->cod_conta);
        $this->assertEquals('15', $resultado[0]->coluna);
        $this->assertEquals('$_rgf{E_2_B1_789_15}', $resultado[0]->currentExpression);

        $this->assertEquals('E_2_B2_654_20', $resultado[1]->variavel, 'Falha: Variável RGF 2 não extraída corretamente.');
        $this->assertEquals('B2', $resultado[1]->anexo);
        $this->assertEquals('654', $resultado[1]->cod_conta);
        $this->assertEquals('20', $resultado[1]->coluna);
        $this->assertEquals('$_rgf{E_2_B2_654_20}', $resultado[1]->currentExpression);
    }

    public function test_restaurar_versao()
    {
        // Criando uma diretoria
        $diretoria = Diretoria::factory()->create();

        // Criando um modelo de análise
        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        // Criando uma cidade
        $cidade = Cidade::factory()->create(['id' => rand(1, 99999), 'nome' => 'Cidade A']);

        // Criando a Certidão
        $certidao = CertidaoPVL::create([
            'cidade_id' => $cidade->id,
            'user_id' => $this->authUser->id,
            'status' => CertidaoStatusEnum::INICIADA,
            'esfera' => Esfera::Municipal,
            'data_criacao' => now()->subDays(5),
            'modelo_analise_id' => $modeloAnalise->id,
        ]);

        // Criando um Formulário associado à Certidão
        $textoInicial = 'Texto original da certidão.';
        $formulario = CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => 1,
            'data_criacao' => now(),
            'data_preenchimento' => now(),
            'texto' => $textoInicial,
            'versao' => 1,
            'user_id' => $this->authUser->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
        ]);

        // Salvando a primeira versão antes da alteração
        CertidaoPVLFormularioVersao::create([
            'versao' => 1,
            'certidao_formulario_id' => $formulario->id,
            'data_preenchimento' => $formulario->data_preenchimento,
            'texto' => $textoInicial,
            'user_id' => $this->authUser->id,
        ]);

        // Alterando o formulário (gerando uma nova versão)
        $novoTexto = 'Texto modificado na versão 2.';
        $formulario->update([
            'texto' => $novoTexto,
            'versao' => 2,
        ]);

        // Pegando a versão a ser restaurada (versão inicial)
        $versaoARestaurar = CertidaoPVLFormularioVersao::where('certidao_formulario_id', $formulario->id)
            ->where('versao', 1)
            ->first();

        // Chamando a função do service para restaurar a versão
        $formularioRestaurado = $this->service->restaurarVersao($versaoARestaurar, $formulario);

        // Verificando se o texto restaurado é igual ao texto original
        $this->assertNotNull($formularioRestaurado, 'A restauração retornou null.');
        $this->assertEquals($textoInicial, $formularioRestaurado->texto, 'O texto restaurado não é o original.');
        $historico = $this->service->getAcoes($formulario);

        $this->assertNotEmpty($historico->items(), 'Falha: O histórico está vazio.');
        $this->assertEquals($historico->items()[0]->acao, CertidaoFormularioHistoricoAcao::RestaurarVersao, 'O texto restaurado não é o original.');
        $this->assertCount(1, $historico, 'Falha: Número de históricos do formulário incorreto.');
    }

    public function test_fetch_certidoes_com_variacoes_de_filtros()
    {
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);
        $modeloAnaliseEstadual = ModeloAnalise::create([
            'nome' => 'Relatório Técnico Estadual',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Estadual,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $cidade1 = Cidade::factory()->create(['id' => rand(1, 99999), 'nome' => 'Cidade A']);
        $cidade2 = Cidade::factory()->create(['id' => rand(1, 99999), 'nome' => 'Cidade B']);

        $unidadeGovernoEstado = UnidadeGestora::factory()->create([
            'cidade_id' => $cidade2->id,
            'tipo_unidade' => TipoUnidade::GovernoDoEstado,
            'subtipo' => SubtipoUnidade::GovernoDoEstado,
        ]);

        $usuario1 = User::factory()->create();
        $usuario2 = User::factory()->create();

        CertidaoPVL::insert([
            [
                'cidade_id' => $cidade1->id,
                'user_id' => $usuario1->id,
                'status' => CertidaoStatusEnum::INICIADA,
                'esfera' => Esfera::Municipal,
                'data_criacao' => now()->subDays(5),
                'modelo_analise_id' => $modeloAnalise->id,
            ],
            [
                'cidade_id' => $cidade2->id,
                'user_id' => $usuario2->id,
                'status' => CertidaoStatusEnum::CONCLUIDA,
                'esfera' => Esfera::Estadual,
                'data_criacao' => now()->subDays(10),
                'modelo_analise_id' => $modeloAnaliseEstadual->id,
            ],
        ]);

        $testCases = [
            'Filtro sem Esfera, Município e Situação (deve listar todos)' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => now()->subDays(7)->format('Y-m-d'),
                    'data_fim' => now()->format('Y-m-d'),
                    'esfera' => null,
                    'municipio' => null,
                    'situacao' => null,
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
            'Filtro apenas por esfera municipal' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => null,
                    'data_fim' => null,
                    'esfera' => [Esfera::Municipal],
                    'municipio' => null,
                    'situacao' => null,
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
            'Filtro apenas por município' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => null,
                    'data_fim' => null,
                    'esfera' => null,
                    'municipio' => [$unidadeGovernoEstado->id],
                    'situacao' => null,
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
            'Filtro apenas por situação (INICIADA)' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => null,
                    'data_fim' => null,
                    'esfera' => null,
                    'municipio' => null,
                    'situacao' => [CertidaoStatusEnum::INICIADA],
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
            'Filtro apenas por situação (CONCLUIDA)' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => null,
                    'data_fim' => null,
                    'esfera' => null,
                    'municipio' => null,
                    'situacao' => [CertidaoStatusEnum::CONCLUIDA],
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
            'Filtro por data (últimos 7 dias)' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => now()->subDays(7)->format('Y-m-d'),
                    'data_fim' => now()->format('Y-m-d'),
                    'esfera' => null,
                    'municipio' => null,
                    'situacao' => null,
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
            'Filtro por data mais ampla (últimos 30 dias)' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => now()->subDays(30)->format('Y-m-d'),
                    'data_fim' => now()->format('Y-m-d'),
                    'esfera' => null,
                    'municipio' => null,
                    'situacao' => null,
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 2,
            ],
            'Filtro todos os filtros compativeis com certidão concluido' => [
                'request' => new \Illuminate\Http\Request([
                    'data_inicio' => now()->subDays(30)->format('Y-m-d'),
                    'data_fim' => now()->format('Y-m-d'),
                    'esfera' => [Esfera::Estadual],
                    'municipio' => $unidadeGovernoEstado->id,
                    'situacao' => [CertidaoStatusEnum::CONCLUIDA],
                    'itemsPerPage' => 10,
                ]),
                'expectedCount' => 1,
            ],
        ];

        foreach ($testCases as $descricao => $case) {
            $response = $this->service->fetchCertidoes($case['request']);
            $dados = $response->toArray();

            $this->assertCount(
                $case['expectedCount'],
                $dados['data'],
                "Falha: {$descricao}. Esperado {$case['expectedCount']}, mas retornou ".count($dados['data'])
            );

            if ($case['expectedCount'] > 0) {
                $this->assertNotEmpty($dados['data'], "Falha: {$descricao} - Nenhuma certidão encontrada quando deveria existir.");
            }
        }
    }

    public function test_set_status_emitida()
    {
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $cidade1 = Cidade::factory()->create(['id' => rand(1, 99999), 'nome' => 'Cidade A']);

        $certidao = CertidaoPVL::create([
            'cidade_id' => $cidade1->id,
            'user_id' => $this->authUser->id,
            'status' => CertidaoStatusEnum::INICIADA,
            'esfera' => Esfera::Municipal,
            'data_criacao' => now()->subDays(5),
            'modelo_analise_id' => $modeloAnalise->id,
        ]);

        $mockService = Mockery::mock($this->service::class)->makePartial();

        $mockService->shouldReceive('downloadFormulariosCertidao')
            ->once()
            ->with(Mockery::on(fn ($arg) => $arg->id === $certidao->id))
            ->andReturn((object) [
                'name' => 'mocked_file.pdf',
                'pdf' => '%PDF-1.4 MOCKED CONTENT',
            ]);

        $resultado = $mockService->setStatusEmitida($certidao);

        $certidao->refresh();

        $this->assertEquals(
            CertidaoStatusEnum::EMITIDA,
            $certidao->status,
            'Falha: O status da certidão não foi atualizado corretamente para EMITIDA.'
        );

        $this->assertEquals(
            ['name', 'pdf'],
            array_keys((array) $resultado),
            'Falha: O retorno do método `setStatusEmitida` não foi o esperado.'
        );

        $this->assertNotNull($resultado->pdf, 'Falha: O PDF não foi gerado.');

        $this->assertStringStartsWith('%PDF-', $resultado->pdf, 'Falha: O conteúdo retornado não parece ser um PDF válido.');
    }

    public function test_reabrir_formulario()
    {
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $cidade = Cidade::factory()->create(['id' => rand(1, 99999), 'nome' => 'Cidade A']);

        $certidao = CertidaoPVL::create([
            'cidade_id' => $cidade->id,
            'user_id' => $this->authUser->id,
            'status' => CertidaoStatusEnum::CONCLUIDA,
            'esfera' => Esfera::Municipal,
            'data_criacao' => now()->subDays(5),
            'modelo_analise_id' => $modeloAnalise->id,
        ]);

        $formulario = CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => 1,
            'data_criacao' => now(),
            'data_preenchimento' => now(),
            'texto' => 'Texto original da certidão.',
            'versao' => 1,
            'user_id' => $this->authUser->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
        ]);

        $formulario->update(['status' => CertidaoFormularioStatus::FINALIZADO]);

        $this->assertEquals(CertidaoFormularioStatus::FINALIZADO, $formulario->status, 'O status não foi atualizado para finalizado corretamente.');
        $service = new CertidaoPVLService;

        $formularioReaberto = $service->formularioReabrir($certidao, $formulario);
        $this->assertEquals(CertidaoFormularioStatus::REABERTO, $formularioReaberto->status, 'O status não foi atualizado para reaberto corretamente.');

    }

    public function test_log_acao()
    {
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $cidade = Cidade::factory()->create(['nome' => 'Cidade A', 'id' => rand(1, 100)]);

        $certidao = CertidaoPVL::create([
            'cidade_id' => $cidade->id,
            'user_id' => $this->authUser->id,
            'status' => CertidaoStatusEnum::INICIADA,
            'esfera' => Esfera::Municipal,
            'data_criacao' => now()->subDays(5),
            'modelo_analise_id' => $modeloAnalise->id,
        ]);

        $formulario = CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => 1,
            'data_criacao' => now(),
            'data_preenchimento' => now(),
            'texto' => 'Texto original da certidão.',
            'versao' => 1,
            'user_id' => $this->authUser->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
        ]);

        $mockService = Mockery::mock(CertidaoPVLService::class)->makePartial();

        $data = [
            'texto' => 'Este é um texto salvo automaticamente pelo AutoSave.',
            'status' => CertidaoFormularioStatus::FINALIZADO,
        ];

        $mockService->storeCertidaoPVL($formulario, $data);

        $this->assertDatabaseHas('sapc.certidao_pvl_formulario_historico_acao', [
            'certidao_formulario_id' => $formulario->id,
        ]);

        $listFormularioVersoes = $this->service->listarVersoes($formulario);
        $this->assertCount(1, $listFormularioVersoes, 'Falha: deve haver ao menos uma versão para este formulário.');
    }

    public function test_save_oficio()
    {
        $mockSiconfiRREO = Mockery::mock('alias:'.\App\Models\SiconfiRREO::class);
        $mockSiconfiRREO->shouldReceive('where')
            ->with([
                'periodo' => '2',
                'anexo' => 'RREO-Anexo 01',
                'cod_conta' => 'ReceitasCorrentes',
                'coluna' => 'PREVISÃO INICIAL',
                'exercicio' => '2022',
            ])
            ->withAnyArgs()
            ->andReturnSelf();

        $mockSiconfiRREO->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '100.50']);

        $mockBuilder = Mockery::mock(\Illuminate\Database\Eloquent\Builder::class);

        $mockBuilder->shouldReceive('where')
            ->withAnyArgs()
            ->andReturnSelf();

        $mockBuilder->shouldReceive('get')
            ->andReturn(collect([
                (object) [
                    'valor' => 100,
                    'variavel' => '$_variavel_alguma_coisa',
                ],
            ]));

        $mockSiconfiRREO->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $mockSiconfiRGF = Mockery::mock('alias:'.\App\Models\SiconfiRGF::class);
        $mockSiconfiRGF->shouldReceive('where')
            ->with([
                'exercicio' => '2024',
                'cod_ibge' => '2704302',
                'periodo' => '2',
                'co_poder' => 'E',
                'anexo' => 'RGF-Anexo 01',
                'cod_conta' => 'ObrigacoesPatronais',
                'coluna' => 'TOTAL (ÚLTIMOS 12 MESES) (a)',
            ])
            ->withAnyArgs()
            ->andReturnSelf();

        $mockSiconfiRGF->shouldReceive('first')
            ->andReturn((object) ['id' => 9999, 'is_extenso' => false, 'valor' => '200.44']);

        $mockSiconfiRGF->shouldReceive('select')
            ->withAnyArgs()
            ->andReturn($mockBuilder);

        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $tiposFormularios = [
            CertidaoPVLTipoModeloFormularioEnum::ANALISADO,
            CertidaoPVLTipoModeloFormularioEnum::NAO_ANALISADO,
            CertidaoPVLTipoModeloFormularioEnum::MARCACAO,
            CertidaoPVLTipoModeloFormularioEnum::CONTEUDO,
        ];

        $modelosFormulario = collect($tiposFormularios)->map(fn ($tipoFormulario) => ModeloFormulario::create([
            'nome' => "Form de teste {$tipoFormulario}",
            'texto' => 'INTRODUÇÃO<br/>1 - Nos termos dos Arts. 31, §1º e 2º, 71, inc. I c/c o 75',
            'status' => StatusModeloFormulario::Ativo,
            'versao' => 1,
            'tipo_formulario' => $tipoFormulario,
        ]));

        foreach ($modelosFormulario as $key => $formulario) {
            ModeloAnaliseFormulario::create([
                'modelo_analise_id' => $modeloAnalise->id,
                'modelo_formulario_id' => $formulario->id,
                'data_inicio' => now()->addYears(-1),
                'data_fim' => now()->addYears(1),
                'ordenacao' => $key,
            ]);
        }

        $cidade = Cidade::factory()->create(['id' => 999]);
        $exerciciosAnalisados = collect(range(2022, 2024))->map(fn ($exercicio) => [
            'esfera' => Esfera::Municipal,
            'cidade_id' => $cidade->id,
            'exercicio' => $exercicio,
            'analisado' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        CidadeExercicioAnalisado::insert($exerciciosAnalisados->toArray());

        $unidade = UnidadeGestora::factory()->create([
            'cidade_id' => $cidade->id,
            'tipo_unidade' => TipoUnidade::Prefeitura,
            'subtipo' => SubtipoUnidade::Prefeitura,
            'esfera' => Esfera::Municipal,
        ]);

        $unidade->diretoria()->associate($diretoria)->save();

        collect(range(2022, 2024))->each(function ($exercicio) use ($cidade) {
            CidadeExercicioAnalisado::updateOrCreate([
                'exercicio' => $exercicio,
                'cidade_id' => $cidade->id,
                'esfera' => Esfera::Municipal,
            ], [
                'analisado' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        });

        $certidao = $this->service->criarCertidao(['ug_id' => $unidade->id]);

        $this->service->atualizarOficio($certidao, '00098');
        $this->assertDatabaseHas('sapc.certidao_pvl', [
            'id' => $certidao->id,
            'oficio' => '00098',
        ]);
    }

    public function test_download_certidao()
    {
        $diretoria = Diretoria::factory()->create();

        $modeloAnalise = ModeloAnalise::create([
            'nome' => 'Relatório Técnico',
            'data_inicio' => now()->subYears(2),
            'data_fim' => now()->addYears(1),
            'tipo_analise' => TipoAnalise::Governo,
            'diretoria_id' => $diretoria->id,
            'esfera' => Esfera::Municipal,
            'categoria_documento' => CategoriaDocumento::CERTIDAO,
        ]);

        $cidade1 = Cidade::factory()->create(['id' => rand(1, 99999), 'nome' => 'Cidade A']);

        $certidao = CertidaoPVL::create([
            'cidade_id' => $cidade1->id,
            'user_id' => $this->authUser->id,
            'status' => CertidaoStatusEnum::INICIADA,
            'esfera' => Esfera::Municipal,
            'data_criacao' => now()->subDays(5),
            'modelo_analise_id' => $modeloAnalise->id,
        ]);

        CertidaoPVLFormulario::create([
            'certidao_id' => $certidao->id,
            'modelo_formulario_id' => 1,
            'data_criacao' => now(),
            'data_preenchimento' => now(),
            'texto' => 'Texto original da certidão.',
            'versao' => 1,
            'user_id' => $this->authUser->id,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
        ]);

        $mock = Mockery::mock('alias:'.Browsershot::class);
        $mock->shouldReceive('html')->once()->andReturnSelf();
        $mock->shouldReceive('newHeadless')->andReturnSelf();
        $mock->shouldReceive('noSandbox')->andReturnSelf();
        $mock->shouldReceive('waitUntilNetworkIdle')->andReturnSelf();
        $mock->shouldReceive('delay')->andReturnSelf();
        $mock->shouldReceive('showBackground')->andReturnSelf();
        $mock->shouldReceive('emulateMedia')->andReturnSelf();
        $mock->shouldReceive('format')->andReturnSelf();
        $mock->shouldReceive('margins')->andReturnSelf();
        $mock->shouldReceive('setOption')->andReturnSelf();
        $mock->shouldReceive('timeout')->andReturnSelf();
        $mock->shouldReceive('*')->andReturnSelf();
        $mock->shouldReceive('pdf')->andReturn('%PDF-1.3 mock');

        $resultado = $this->service->downloadFormulariosCertidao($certidao);

        $this->assertNotNull($resultado->pdf, 'O método deve retornar um pdf.');
        $this->assertNotNull($resultado->name, 'O método deve retornar um nome.');

    }
}
