<?php

namespace App\Http\Controllers;

use App\Enums\Sapc\CertidaoFormularioStatus;
use App\Http\Requests\Sapc\CertidaoPVLFormularioHistoricoAcaoRequest;
use App\Http\Requests\Sapc\CertidaoPVLFormularioRequest;
use App\Http\Requests\Sapc\CriarCertidaoRequest;
use App\Http\Resources\Sapc\CertidaoPVLFormularioHistoricoAcaoResource;
use App\Http\Resources\Sapc\CertidaoPVLFormularioResource;
use App\Http\Resources\Sapc\CertidaoPVLFormularioVersaoResource;
use App\Http\Resources\Sapc\CertidaoPVLFormularioVersoesResource;
use App\Http\Resources\Sapc\CertidaoPVLResource;
use App\Models\Sapc\CertidaoPVLFormulario;
use App\Models\Sapc\CertidaoPVLFormularioVersao;
use App\Models\Sapc\CertidaoPVL;
use App\Services\CertidaoPVLService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class CertidaoPVLController
{
    protected CertidaoPVLService $certidaoPVLService;

    public function __construct(CertidaoPVLService $certidaoPVLService)
    {
        $this->certidaoPVLService = $certidaoPVLService;
    }

    public function emitirCertidao(CertidaoPVL $certidao)
    {
        $data = $this->certidaoPVLService->setStatusEmitida($certidao);;

        return response()->streamDownload(function () use ($data) {
            echo $data->pdf;
        }, $data->name, ['Content-Type' => 'application/pdf']);
    }
    

    public function criarCertidao(CriarCertidaoRequest $request)
    {
        $certidaoPVL = $this->certidaoPVLService->criarCertidao($request->all());

        return new CertidaoPVLResource($certidaoPVL);
    }

    public function certidaoShow(CertidaoPVL $certidao)
    {
        return new CertidaoPVLResource($certidao);
    }


    public function fetchCertidoes(Request $request)
    {
        $certidoes = $this->certidaoPVLService->fetchCertidoes($request);        

        $response = CertidaoPVLResource::collection($certidoes);

        return $response;
    }

    public function certidoesFormularios($certidao)
    {
        $certidoesFormularios = $this->certidaoPVLService->listarCertidaoFormularios($certidao);

        return CertidaoPVLFormularioResource::collection($certidoesFormularios);
    }

    public function formularioShow(CertidaoPVL $certidao, CertidaoPVLFormulario $certidaoPVLFormulario)
    {
        return new CertidaoPVLFormularioResource($certidaoPVLFormulario);
    }

    public function formularioGetVersoes(CertidaoPVL $certidao, CertidaoPVLFormulario $certidaoPVLFormulario)
    {
        $result = $this->certidaoPVLService->listarVersoes($certidaoPVLFormulario);

        return CertidaoPVLFormularioVersoesResource::collection($result);
    }

    public function formularioReabrir(CertidaoPVL $certidao, CertidaoPVLFormulario $certidaoPVLFormulario, CertidaoPVLService $certidaoPVLService)
    {
        $certidaoPVLFormulario = $certidaoPVLService->formularioReabrir($certidao, $certidaoPVLFormulario);

        return new CertidaoPVLFormularioResource($certidaoPVLFormulario);
    }

    public function formularioGetAcoes(CertidaoPVL $certidao, CertidaoPVLFormulario $certidaoPVLFormulario)
    {
        $acoes = $this->certidaoPVLService->getAcoes($certidaoPVLFormulario);

        return CertidaoPVLFormularioHistoricoAcaoResource::collection($acoes);
    }

    public function formularioStore(CertidaoPVL $certidao, CertidaoPVLFormulario  $certidaoPVLFormulario, CertidaoPVLFormularioRequest $request)
    {
        $data = $request->all();

        if (($certidaoPVLFormulario->texto === $data['texto']) && ($data['status'] != CertidaoFormularioStatus::FINALIZADO)) {
            abort(400, 'Os textos não podem ser iguais.');
        }
        
        return $this->certidaoPVLService
            ->storeCertidaoPVL(
                $certidaoPVLFormulario, 
                $data
            );
    }

    public function formularioRestoreVersao($certidao, CertidaoPVLFormulario $certidaoPVLFormulario, CertidaoPVLFormularioVersao $certidaoPVLVersaoFormulario)
    {
        $versaoAtual = $this->certidaoPVLService->restaurarVersao($certidaoPVLVersaoFormulario, $certidaoPVLFormulario);
      
        return response()->json(['data' => $versaoAtual]);
    }

    public function formularioGetVersao($certidaoPVLFormulario, CertidaoPVLFormularioVersao $certidaoPVLVersaoFormulario)
    {
        if ($certidaoPVLVersaoFormulario->certidao_formulario_id != $certidaoPVLFormulario) {
            throw new ModelNotFoundException('O Formulário não possui essa versão');
        }

        $certidaoPVLVersaoFormulario->load(['user', 'formulario']);
        return response()->json(new CertidaoPVLFormularioVersaoResource($certidaoPVLVersaoFormulario));
    }

    public function certidaoPVLDownload(CertidaoPVL $certidao)
    {
        $data = $this->certidaoPVLService->downloadFormulariosCertidao($certidao);

        return response()->streamDownload(function () use ($data) {
            echo $data->pdf;
        }, $data->name, ['Content-Type' => 'application/pdf']);
    }

    public function autoSaveCertidao(CertidaoPVL $certidao, CertidaoPVLFormulario $certidaoPVLFormulario, Request $request)
    {
        try {
            $this->certidaoPVLService
                ->autoSaveCertidao($certidaoPVLFormulario, $request->input('text'));

            return response()->json(['success' => 'Certidão salva Automáticamente!']);
        } catch (Exception $e) {
            Log::error("Falha no salvamento automático da certidão: {$e->getMessage()}");

            return response()->json(
                ['error' => $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function recalcularCertidao(CertidaoPVL $certidao)
    {
        $this->certidaoPVLService->recalcularCertidao($certidao);

        return response()->json(['message' => 'Certidão recalculada!']);
    }

    public function updateCertidaoOficio(CertidaoPVL $certidao, Request $request)
    {
        $this->certidaoPVLService->atualizarOficio($certidao, $request->input('oficio'));
    }
}
