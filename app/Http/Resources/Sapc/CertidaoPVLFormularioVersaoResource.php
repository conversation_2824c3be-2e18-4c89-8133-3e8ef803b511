<?php

namespace App\Http\Resources\Sapc;

use App\Http\Resources\UserResource;
use Caxy\HtmlDiff\HtmlDiff;
use Caxy\HtmlDiff\HtmlDiffConfig;

use Illuminate\Http\Resources\Json\JsonResource;

class CertidaoPVLFormularioVersaoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $htmlDiffCurrent = new HtmlDiff($this->formulario->texto, $this->texto);
        $htmlDiffCurrent->getConfig()->setPurifierCacheLocation(storage_path("framework/cache"));

        $htmlDiffOld = new HtmlDiff($this->texto, $this->formulario->texto);
        $htmlDiffOld->getConfig()->setPurifierCacheLocation(storage_path("framework/cache"));

        $diff_current = $htmlDiffCurrent->build();
        $diff_old = $htmlDiffOld->build();

        return [

            'id' => $this->id,
            'certidao_formulario_id' => $this->certidao_formulario_id,
            'data_preenchimento' => $this->data_preenchimento,
            'texto' => $this->texto,
            'versao' => $this->versao,
            'user_id' => $this->user_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            'diff_current' => $diff_current,
            'diff_old' => $diff_old,

            'formulario' => new CertidaoPVLFormularioResource($this->formulario),
            'usuario' => new UserResource($this->user)
        ];
    }
}
