<?php

namespace App\Http\Resources\Sapc;

use App\Http\Resources\UserResource;
use Illuminate\Http\Resources\Json\JsonResource;

class CertidaoPVLFormularioHistoricoAcaoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request)
    {
        return [
            'id'                     => $this->id,
            'certidao_formulario_id' => $this->certidao_formulario_id,
            'acao'                   => $this->acao,
            'valor'                  => $this->valor,
            'user_id'                => $this->user_id,
            'data_acao'              => $this->data_acao,
            'created_at'             => $this->created_at,
            'updated_at'             => $this->updated_at,
            'acao_texto'             => $this->acao_texto,
            'user' => new UserResource($this->user),
        ];
    }
}

