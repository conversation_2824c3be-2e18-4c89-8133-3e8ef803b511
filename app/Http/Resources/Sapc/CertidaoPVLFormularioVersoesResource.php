<?php

namespace App\Http\Resources\Sapc;

use App\Http\Resources\UserResource;
use Caxy\HtmlDiff\HtmlDiff;
use Caxy\HtmlDiff\HtmlDiffConfig;

use Illuminate\Http\Resources\Json\JsonResource;

class CertidaoPVLFormularioVersoesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'certidao_formulario_id' => $this->certidao_formulario_id,
            'data_preenchimento' => $this->data_preenchimento,
            'versao' => $this->versao,
            'user_id' => $this->user_id,
            'usuario' => $this->user ? [
                'id' => $this->user->id,
                'name' => $this->user->name,
            ] : null,
        ];
    }
}
