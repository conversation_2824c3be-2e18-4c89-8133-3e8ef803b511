<?php

namespace App\Http\Resources\Sapc;

use App\Enums\Sapc\StatusAnaliseFormulario;
use Illuminate\Http\Resources\Json\JsonResource;

class CertidaoPVLFormularioResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'certidao_id' => $this->certidao_id,
            'modelo_formulario_id' => $this->modelo_formulario_id,
            'data_criacao' => $this->data_criacao,
            'data_preenchimento' => $this->data_preenchimento,
            'texto' => $this->texto,
            'versao' => $this->versao,
            'user_id' => $this->user_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'exercicio' => $this->exercicio,
            'nome' => $this->modeloFormulario->nome,
            'nome_formulario' => ($this->exercicio) ? ($this->exercicio == date('Y') ? $this->exercicio . ' - Exercício Em Curso': $this->exercicio. ' - ' . $this->modeloFormulario->nome) : $this->modeloFormulario->nome,
            'status' => $this->status,
            'status_descricao' => $this->status->description,
            'user_finalizado' => ($this->status == StatusAnaliseFormulario::Finalizado && !is_null($this->data_preenchimento))
                ? 'Finalizado por ' . $this->user->name . ' em ' . $this->data_preenchimento->format('d/m/Y H:i') ?? null
                : null,

            'texto_auto_save' => $this->texto_auto_save,
        ];
    }
}
