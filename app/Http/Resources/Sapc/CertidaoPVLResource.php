<?php

namespace App\Http\Resources\Sapc;

use App\Enums\Esfera;
use Illuminate\Http\Resources\Json\JsonResource;

class CertidaoPVLResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'modelo' => $this->modeloAnalise,
            'cidade' => $this->esfera->is(Esfera::Estadual) ? 'Governo do Estado' : $this->cidade->nome,
            'responsavel' => $this->user->name,
            'data_criacao' => $this->data_criacao,
            'data_conclusao' => $this->data_conclusao,
            'assinatura' => $this->assinatura,
            'situacao' => $this->status->description,
            'esfera' => $this->esfera->description,
            'oficio' => $this->oficio,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
