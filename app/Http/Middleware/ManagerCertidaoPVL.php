<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ManagerCertidaoPVL
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  ...$guards
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check()) {
            $isLocal = config('app.env') === 'local';
            $isPostman = str_contains($request->header('User-Agent'), 'Postman');
            $validToken = config('auth.api_token_fallback');
            $requestToken = $request->header('X-API-Token');

            if ($isLocal && $isPostman && $requestToken === $validToken) {
                return $next($request);
            }

            return response()->json([
                'status' => 'warn',
                'message' => 'Você não está autenticado ou seu login expirou.'
            ], Response::HTTP_UNAUTHORIZED);
        }

        if (Auth::user()->isDiretor()) {
            return response()->json([
                'status'  => 'error',
                'message' => 'Usuário sem permissão para manipular analise'
            ], 403);
        }

        return $next($request);
    }
}
