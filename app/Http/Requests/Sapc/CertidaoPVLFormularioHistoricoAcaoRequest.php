<?php

namespace App\Http\Requests\Sapc;

use App\Enums\Sapc\CertidaoFormularioHistoricoAcao;
use Illuminate\Foundation\Http\FormRequest;

class CertidaoPVLFormularioHistoricoAcaoRequest extends FormRequest
{
    public function authorize()
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'acao'  => 'required|in:' . implode(',', CertidaoFormularioHistoricoAcao::getValues()),
            'valor' => 'required',
        ];
    }
}
