<?php

namespace App\Http\Requests\Sapc;

use Illuminate\Foundation\Http\FormRequest;

class CertidaoPVLFormularioRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'data_preenchimento' => 'nullable|date',
            'texto' => 'nullable|string',
            'status' => 'required|string',
        ];
    }
}
