<?php

namespace App\Http\Requests\Sapc;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class CriarCertidaoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'ug_id' => [
                'required',
                'numeric',
                function ($attribute, $value, $fail) {
                    if (!DB::table('unidade_gestoras')->where('id', $value)->exists()) {
                        $fail("Ente não encontrado no sistema.");
                    }
                }
            ],
        ];
    }
}
