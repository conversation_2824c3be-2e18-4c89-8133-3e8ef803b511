<?php

namespace App\Jobs;

use App\Enums\Sapc\CertidaoStatusEnum;
use App\Models\Sapc\CertidaoPVL;
use App\Models\Sapc\Parametrizacao;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ExpiredCertidaoPVLJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 3600;

    public $tries = 5;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('ExpiredCertidaoPVLJob - Job iniciado');

        $diasExpiracao = Parametrizacao::where('chave', 'certidao_dias_expiracao')->value('valor');
        Log::info('Dias de expiração: ' . $diasExpiracao);

        if (!is_numeric($diasExpiracao)) {
            Log::error('Erro: O valor de dias_expiracao não é um número válido.');
            return;
        }

        CertidaoPVL::where('status', CertidaoStatusEnum::EMITIDA)
            ->whereRaw('(CURRENT_DATE - data_criacao::date) > ?', [$diasExpiracao])
            ->update(['status' => CertidaoStatusEnum::VENCIDA]);

        Log::info('ExpiredCertidaoPVLJob - Job finalizado');
    }

}
