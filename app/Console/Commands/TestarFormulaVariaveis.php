<?php

namespace App\Console\Commands;

use App\Models\Sapc\Analise;
use App\Models\Sapc\VariavelContaGoverno;
use App\Services\SapcService;
use Illuminate\Console\Command;

class TestarFormulaVariaveis extends Command
{
    protected $signature = 'testar:formula-variaveis';

    protected $description = 'Testa o processamento de variáveis em fórmulas SAPC';

    public function handle()
    {
        $this->info('=== Teste de Processamento de Variáveis em Fórmulas ===');

        // Verificar se existem variáveis
        $totalVariaveis = VariavelContaGoverno::count();
        $this->info("Total de variáveis disponíveis: {$totalVariaveis}");

        if ($totalVariaveis == 0) {
            $this->error('Nenhuma variável encontrada no banco de dados.');
            return 1;
        }

        // Listar variáveis disponíveis
        $variaveis = VariavelContaGoverno::select('id', 'nome')->get();
        $this->info('Variáveis disponíveis:');
        foreach ($variaveis as $variavel) {
            $this->line("  - ID: {$variavel->id}, Nome: {$variavel->nome}");
        }

        // Criar uma análise fictícia para teste
        $analise = new Analise([
            'id' => 999,
            'unidade_gestora_id' => 1717,
            'exercicio' => 2024,
        ]);

        // Testar extração de variáveis
        $this->info("\n=== Teste de Extração de Variáveis ===");

        $formulas = [
            '{Impostos, Taxas e Contribuições de Melhoria} >= 1000 ? \'adequado\' : \'inadequado\'',
            '{Amortização Da Dívida} + {Impostos, Taxas e Contribuições de Melhoria}',
            '{VARIAVEL_INEXISTENTE} * 100',
        ];

        foreach ($formulas as $index => $formula) {
            $this->info("\nTeste " . ($index + 1) . ":");
            $this->line("Fórmula: {$formula}");

            // Extrair variáveis
            preg_match_all('/\{([^}]+)\}/', $formula, $matches);
            $variaveisEncontradas = array_unique($matches[1]);

            $this->line("Variáveis extraídas: " . count($variaveisEncontradas));
            foreach ($variaveisEncontradas as $var) {
                $this->line("  - {$var}");

                // Verificar se existe no banco
                $existe = VariavelContaGoverno::where('nome', $var)->exists();
                $this->line("    Existe no banco: " . ($existe ? 'SIM' : 'NÃO'));
            }
        }

        // Testar processamento completo (simulado)
        $this->info("\n=== Teste de Processamento Completo ===");

        $sapcService = new SapcService();
        $textoTeste = "Resultado: ==#({Impostos, Taxas e Contribuições de Melhoria} >= 1000 ? 'adequado' : 'inadequado')#";

        $this->line("Texto original: {$textoTeste}");

        // Testar getFormulas diretamente
        $formulas = $sapcService->getFormulas($textoTeste);
        $this->info("Fórmulas encontradas pelo getFormulas: " . count($formulas));
        foreach($formulas as $f) {
            $this->line("  - {$f}");
        }

        try {
            // Nota: Este teste pode falhar se não houver dados na matriz, mas mostrará se a lógica está funcionando
            $resultado = $sapcService->parseTexto($analise, $textoTeste);
            $this->info("Resultado processado: {$resultado}");
        } catch (\Exception $e) {
            $this->warn("Erro durante processamento: " . $e->getMessage());
            $this->line("Trace: " . $e->getTraceAsString());
        }

        $this->info("\n=== Teste Concluído ===");
        return 0;
    }
}
