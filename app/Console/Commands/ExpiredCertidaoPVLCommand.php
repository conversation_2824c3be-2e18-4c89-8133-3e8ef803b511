<?php

namespace App\Console\Commands;

use App\Jobs\ExpiredCertidaoPVLJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExpiredCertidaoPVLCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'expired-certidao-pvl:certidao';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Atualização de status de certidões PVL expiradas';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            Log::info('Comand Disparada: ExpiredCertidaoPVLCommand');
            ExpiredCertidaoPVLJob::dispatch();
            
        } catch (\Exception $e) {
            Log::error('Falha ao agendar job ExpiredCertidaoPVLCommand: '.$e->getMessage());
        }
    }
}