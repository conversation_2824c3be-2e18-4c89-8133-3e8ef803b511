<?php

namespace App\Services;

use App\Enums\Esfera;
use App\Enums\Sapc\CategoriaDocumento;
use App\Enums\Sapc\CertidaoFormularioHistoricoAcao;
use App\Enums\Sapc\CertidaoFormularioStatus;
use App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum;
use App\Enums\Sapc\CertidaoStatusEnum;
use App\Enums\SubtipoUnidade;
use App\Enums\TipoUnidade;
use App\Models\Cidade;
use App\Models\Sapc\CertidaoPVL;
use App\Models\Sapc\CertidaoPVLFormulario;
use App\Models\Sapc\CertidaoPVLFormularioHistoricoAcao;
use App\Models\Sapc\CertidaoPVLFormularioVersao;
use App\Models\Sapc\CidadeExercicioAnalisado;
use App\Models\Sapc\ModeloAnalise;
use App\Models\Sapc\ModeloElemento;
use App\Models\Sapc\ModeloFormulario;
use App\Models\Sapc\Parametrizacao;
use App\Models\SiconfiRGF;
use App\Models\SiconfiRREO;
use App\Models\SiconfiRREOAnexo12RREO;
use App\Models\SiconfiRREOAnexo8Indicadores;
use App\Models\UnidadeGestora;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

class CertidaoPVLService
{
    private $variaveis = [];

    private const PDF_TOP_MARGIN = 0;

    private const PDF_RIGHT_MARGIN = 0;

    private const PDF_LEFT_MARGIN = 0;

    private const PDF_BOTTOM_MARGIN = 0;

    private const PDF_FORMAT = 'A4';

    private const PDF_MEDIA = 'print';

    public function criarCertidao($data)
    {
        $ug = UnidadeGestora::findOrFail($data['ug_id']);

        $unidadeGestoraEsfera = $ug->esfera;

        $modeloAnalise = ModeloAnalise::with(['modelosAnalisesFormularios' => function ($query) {
            $query->where(function ($subQuery) {
                $subQuery->where('data_fim', '>=', today())
                    ->orWhereNull('data_fim');
            });
        }])
            ->where([
                'categoria_documento' => CategoriaDocumento::CERTIDAO,
                'esfera' => $unidadeGestoraEsfera->value,
            ])
            ->where(function ($query) {
                $query
                    ->where('data_inicio', '<=', today())
                    ->where(function ($subQuery) {
                        $subQuery->where('data_fim', '>=', today())
                            ->orWhereNull('data_fim');
                    });
            })
            ->first();

        $requiredFormulariosType = [
            CertidaoPVLTipoModeloFormularioEnum::ANALISADO => false,
            CertidaoPVLTipoModeloFormularioEnum::NAO_ANALISADO => false,
            CertidaoPVLTipoModeloFormularioEnum::MARCACAO => false,
        ];

        if (empty($modeloAnalise)) {
            throw new Exception("Modelo não encontrado para esfera {$unidadeGestoraEsfera->description}", 400);
        }

        foreach ($modeloAnalise->formularios as $formulario) {
            if (isset($requiredFormulariosType[$formulario->tipo_formulario->value])) {
                $requiredFormulariosType[$formulario->tipo_formulario->value] = true;
            }
        }

        if (in_array(false, $requiredFormulariosType, true)) {
            throw new Exception('Não é possível gerar Certidão para este Ente', 400);
        }

        $cidade = Cidade::whereHas('unidades', fn ($q) => $q->where('id', $data['ug_id'])
        )
            ->first();

        $unidadeGestora = UnidadeGestora::with(['diretoria'])
            ->when($modeloAnalise->esfera === Esfera::Municipal,
                fn ($query) => $query->where([
                    'tipo_unidade' => TipoUnidade::Prefeitura,
                    'subtipo' => SubtipoUnidade::Prefeitura,
                ]),
                fn ($query) => $query->where([
                    'tipo_unidade' => TipoUnidade::GovernoDoEstado,
                    'subtipo' => SubtipoUnidade::GovernoDoEstado,
                ])
            )
            ->where('cidade_id', $cidade->id)
            ->first();

        if (is_null($unidadeGestora?->diretoria)) {
            throw new Exception("A Unidade Gestora {$ug->nome} não possui diretoria vinculada.", 400);
        }

        $data['cidade_id'] = $cidade->id;
        $data['esfera'] = $modeloAnalise->esfera;
        $data['status'] = CertidaoStatusEnum::INICIADA;
        $data['data_criacao'] = Carbon::now();
        $data['data_finalizada'] = null;
        $data['modelo_analise_id'] = $modeloAnalise->id;
        $data['user_id'] = auth()->user()->id;

        $certidao = CertidaoPVL::create($data);

        $modeloFormularioAnalisado = ModeloFormulario::where('tipo_formulario', CertidaoPVLTipoModeloFormularioEnum::ANALISADO)
            ->whereHas('modelosAnalisesFormularios', function ($query) use ($modeloAnalise) {
                $query->where('modelo_analise_id', $modeloAnalise->id);
                $query->where(function ($subQuery) {
                    $subQuery->where('data_fim', '>=', today())
                        ->orWhereNull('data_fim');
                });
            })
            ->first();
        $modeloFormularioNaoAnalisado = ModeloFormulario::where('tipo_formulario', CertidaoPVLTipoModeloFormularioEnum::NAO_ANALISADO)
            ->whereHas('modelosAnalisesFormularios', function ($query) use ($modeloAnalise) {
                $query->where('modelo_analise_id', $modeloAnalise->id);
                $query->where(function ($subQuery) {
                    $subQuery->where('data_fim', '>=', today())
                        ->orWhereNull('data_fim');
                });
            })
            ->first();

        $modeloFormularioMarcacao = ModeloFormulario::where('tipo_formulario', CertidaoPVLTipoModeloFormularioEnum::MARCACAO)
            ->whereHas('modelosAnalisesFormularios', function ($query) use ($modeloAnalise) {
                $query->where('modelo_analise_id', $modeloAnalise->id);
                $query->where(function ($subQuery) {
                    $subQuery->where('data_fim', '>=', today())
                        ->orWhereNull('data_fim');
                });
            })
            ->first();

        $dadosFormularioStructure = [
            'certidao_id' => $certidao->id,
            'data_criacao' => Carbon::now(),
            'data_preenchimento' => null,
            'user_id' => auth()->user()->id,
            'versao' => 1,
            'exercicio' => null,
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
        ];

        $dadosFormularios = [];

        foreach ($certidao->modeloAnalise->formularios as $certidaoPVLModeloFormulario) {
            $isMarcacao = $modeloFormularioMarcacao && $certidaoPVLModeloFormulario->id == $modeloFormularioMarcacao->id;
            $isFormularioAnalisado = $modeloFormularioAnalisado && $certidaoPVLModeloFormulario->id == $modeloFormularioAnalisado->id;
            $isFormularioNaoAnalisado = $modeloFormularioNaoAnalisado && $certidaoPVLModeloFormulario->id == $modeloFormularioNaoAnalisado->id;

            if ($isFormularioAnalisado || $isFormularioNaoAnalisado) {
                continue;
            }

            if ($isMarcacao) {
                $limiteExercicio = today()->lessThan(Carbon::create(today()->year, 4, 30)) ? 7 : 6;

                $cidadesExercicioAnalisado = CidadeExercicioAnalisado::where('cidade_id', $cidade->id)
                    ->where('analisado', true)
                    ->where('exercicio', '>=', date('Y') - $limiteExercicio)
                    ->orderBy('exercicio', 'desc')
                    ->limit(1)
                    ->first();

                if ($cidadesExercicioAnalisado) {
                    $limiteExercicio--;
                }

                $cidadesExercicio = CidadeExercicioAnalisado::where('cidade_id', $cidade->id)
                    ->where('analisado', false)
                    ->orderBy('exercicio', 'desc')
                    ->limit($limiteExercicio)
                    ->get();

                if ($cidadesExercicioAnalisado) {
                    $cidadesExercicio[] = $cidadesExercicioAnalisado;
                }

                $this->variaveis = [];
                $codigoIbge = $certidao->esfera->is(Esfera::Estadual) ? 27 : $cidade->codigo_ibge;

                foreach ($cidadesExercicio as $cidadeExercicio) {

                    if ($cidadeExercicio->analisado) {
                        if ($modeloFormularioAnalisado) {
                            $textoFormulario = $modeloFormularioAnalisado->texto;
                            $dadosFormularioStructure['modelo_formulario_id'] = $modeloFormularioAnalisado->id;
                        }
                    } else {
                        if ($modeloFormularioNaoAnalisado) {
                            $textoFormulario = $modeloFormularioNaoAnalisado->texto;
                            $dadosFormularioStructure['modelo_formulario_id'] = $modeloFormularioNaoAnalisado->id;
                        }
                    }

                    $varsRREO = SiconfiRREO::select(
                        'valor',
                        DB::raw("CONCAT('{',periodo, '_', anexo, '_', cod_conta, '_', coluna,'}') as variavel")
                    )->where([
                        'exercicio' => $cidadeExercicio->exercicio,
                        'cod_ibge' => $codigoIbge,
                    ])
                        ->get()
                        ->pluck('valor', 'variavel')
                        ->toArray();

                    $varsRGF = SiconfiRGF::select(
                        'valor',
                        DB::raw("CONCAT('{',co_poder, '_',periodo, '_', anexo, '_', cod_conta, '_', coluna,'}') as variavel")
                    )->where([
                        'exercicio' => $cidadeExercicio->exercicio,
                        'cod_ibge' => $codigoIbge,
                    ])
                        ->get()
                        ->pluck('valor', 'variavel')
                        ->toArray();

                    $varsRREO8 = SiconfiRREOAnexo8Indicadores::select(
                        'val_indi',
                        DB::raw("CONCAT('{',num_peri, '_',nom_indi,'}') as variavel")
                    )->where([
                        'num_ano' => $cidadeExercicio->exercicio,
                        'cod_muni' => substr($codigoIbge, 0, -1),
                    ])
                        ->get()
                        ->pluck('val_indi', 'variavel')
                        ->toArray();

                    $varsRREO12 = SiconfiRREOAnexo12RREO::select(
                        'valor1',
                        DB::raw("CONCAT('{',periodo, '_',descricao,'}') as variavel")
                    )->where([
                        'ano' => $cidadeExercicio->exercicio,
                        'municipio' => substr_replace($codigoIbge, '.', -1, 0),
                    ]);

                    $varsRREO12 = $varsRREO12->get()
                        ->pluck('valor1', 'variavel')
                        ->toArray();

                    $this->variaveis = array_merge($varsRGF, $varsRREO, $varsRREO8, $varsRREO12);

                    $dadosFormularioStructure['exercicio'] = $cidadeExercicio->exercicio;
                    $dadosFormularioStructure['texto'] = $this->parseTexto(
                        $certidao,
                        $textoFormulario,
                        $cidadeExercicio->exercicio,
                        $cidade
                    );

                    $dadosFormularios[] = $dadosFormularioStructure;
                }
            } else {
                $dadosFormularioStructure['modelo_formulario_id'] = $certidaoPVLModeloFormulario->id;
                $dadosFormularioStructure['exercicio'] = null;
                $dadosFormularioStructure['texto'] = $this->parseTexto(
                    $certidao,
                    $certidaoPVLModeloFormulario->texto,
                    date('Y'),
                    $cidade
                );
                $dadosFormularios[] = $dadosFormularioStructure;
            }
        }

        if (! empty($dadosFormularios)) {
            CertidaoPVLFormulario::insert($dadosFormularios);
        }

        return $certidao;
    }

    public function calcularVariavelRREO($exercicio, $codIbge, $variavel)
    {
        if (!isset($variavel->id)) {
            return 'N/A';
        }

        $varRREO = SiconfiRREO::where([
            'exercicio' => $exercicio,
            'cod_ibge' => $codIbge,
            'periodo' => $variavel->periodo,
            'anexo' => $variavel->anexo,
            'cod_conta' => $variavel->cod_conta,
            'coluna' => $variavel->coluna,
        ])->first();

        if ($varRREO) {
            return $variavel->is_extenso ? $varRREO->valor_extenso : $varRREO->valor;
        }

        if ($exercicio == date('Y')) {

            for ($periodo = $variavel->periodo; $periodo >= 0; $periodo--) {
                $varRREO = SiconfiRREO::where([
                    'exercicio' => $exercicio,
                    'cod_ibge' => $codIbge,
                    'periodo' => $periodo,
                    'anexo' => $variavel->anexo,
                    'cod_conta' => $variavel->cod_conta,
                    'coluna' => $variavel->coluna,
                ])->first();

                if ($varRREO) {
                    return $variavel->is_extenso ? $varRREO->valor_extenso : $varRREO->valor;
                }
            }
        }

        return 0;
    }

    public function calcularVariavelRGF($exercicio, $codIbge, $variavel)
    {
        if (!isset($variavel->id)) {
            return 'N/A';
        }

        $varRGF = SiconfiRGF::where([
            'exercicio' => $exercicio,
            'cod_ibge' => $codIbge,
            'periodo' => (int) $variavel->periodo,
            'co_poder' => $variavel->poder,
            'anexo' => $variavel->anexo,
            'cod_conta' => $variavel->cod_conta,
            'coluna' => html_entity_decode($variavel->coluna),
        ]);
        $varRGF = $varRGF->first();

        if ($varRGF) {
            return $variavel->is_extenso ? $varRGF->valor_extenso : $varRGF->valor;
        }

        if ($exercicio == date('Y')) {

            for ($periodo = $variavel->periodo; $periodo >= 0; $periodo--) {
                $varRGF = SiconfiRGF::where([
                    'exercicio' => $exercicio,
                    'cod_ibge' => $codIbge,
                    'periodo' => (int) $periodo,
                    'co_poder' => $variavel->poder,
                    'anexo' => $variavel->anexo,
                    'cod_conta' => $variavel->cod_conta,
                    'coluna' => ($variavel->coluna),
                ]);

                $varRGF = $varRGF->first();

                if ($varRGF) {
                    return $variavel->is_extenso ? $varRGF->valor_extenso : $varRGF->valor;
                }
            }
        }

        return 0;
    }

    public function calcularVariavelRREO8Indicadores($exercicio, $codIbge, $variavel)
    {
        if (!isset($variavel->id)) {
            return 'N/A';
        }

        $varRREO = SiconfiRREOAnexo8Indicadores::where([
            'num_ano' => $exercicio,
            'cod_muni' => $codIbge,
            'num_peri' => $variavel->periodo,
            'nom_indi' => $variavel->nome,
        ])->first();

        if ($varRREO) {
            return $variavel->is_extenso ? $varRREO->val_indi : $varRREO->val_indi;
        }

        if ($exercicio == date('Y')) {

            for ($periodo = $variavel->periodo; $periodo >= 0; $periodo--) {
                $varRREO = SiconfiRREOAnexo8Indicadores::where([
                    'num_ano' => $exercicio,
                    'cod_muni' => $codIbge,
                    'num_peri' => $periodo,
                    'nom_indi' => $variavel->nome,
                ])->first();

                if ($varRREO) {
                    return $variavel->is_extenso ? $varRREO->val_indi : $varRREO->val_indi;
                }
            }
        }

        return 0;
    }

    public function calcularVariavelRREO12($exercicio, $codIbge, $variavel)
    {
        if (!isset($variavel->id)) {
            return 'N/A';
        }

        $varRREO = SiconfiRREOAnexo12RREO::where([
            'ano' => $exercicio,
            'municipio' => $codIbge,
            'periodo' => $variavel->periodo,
            'descricao' => $variavel->nome,
        ])->first();

        if ($varRREO) {
            return $variavel->is_extenso ? $varRREO->valor1 : $varRREO->valor1;
        }

        if ($exercicio == date('Y')) {

            for ($periodo = $variavel->periodo; $periodo >= 0; $periodo--) {
                $varRREO = SiconfiRREOAnexo12RREO::where([
                    'ano' => $exercicio,
                    'municipio' => $codIbge,
                    'periodo' => $periodo,
                    'descricao' => $variavel->nome,
                ])->first();

                if ($varRREO) {
                    return $variavel->is_extenso ? $varRREO->valor1 : $varRREO->valor1;
                }
            }
        }

        return 0;
    }

    public function getVarsRREO8Indicadores($texto)
    {
        $result = [];
        if (preg_match_all('/\$_rreo8_ind(-([a-zA-Z0-9]+))?\{(.*?)\}/', $texto, $matches)) {
            foreach ($matches[3] as $index => $variavel) {
                $isExtenso = ($matches[2][$index] ?? null) === 'ext';

                [$periodo, $nome] = explode('_', $variavel);

                $objVarRREO8 = SiconfiRREOAnexo8Indicadores::where([
                    'num_peri' => $periodo,
                    'nom_indi' => $nome,
                ])
                    ->first();

                if ($objVarRREO8) {
                    $result[] = (object) [
                        'id' => $objVarRREO8->id,
                        'variavel' => $variavel,
                        'periodo' => $periodo,
                        'nome' => $nome,
                        'currentExpression' => $matches[0][$index],
                        'is_extenso' => $isExtenso,
                    ];
                } else {
                    $result[] = (object) [
                        'variavel' => $variavel,
                        'periodo' => $periodo,
                        'nome' => $nome,
                        'currentExpression' => $matches[0][$index],
                        'is_extenso' => $isExtenso,
                    ];

                    Log::debug('[SAPC] Variável de RREO8 não encontrada: '.$variavel);
                }
            }
        }

        return $result;
    }

    public function getVarsRREO12($texto)
    {
        $result = [];
        if (preg_match_all('/\$_rreo12(-([a-zA-Z0-9]+))?\{(.*?)\}/', $texto, $matches)) {
            foreach ($matches[3] as $index => $variavel) {
                $isExtenso = ($matches[2][$index] ?? null) === 'ext';
                $variavel = str_replace('&nbsp;', ' ', $variavel);
                $variavel = html_entity_decode($variavel);

                [$periodo, $nome] = explode('_', $variavel);

                $objVarRREO12 = SiconfiRREOAnexo12RREO::where([
                    'periodo' => $periodo,
                    'descricao' => $nome,
                ]);

                $objVarRREO12 = $objVarRREO12->first();

                if ($objVarRREO12) {
                    $result[] = (object) [
                        'id' => $objVarRREO12->id,
                        'variavel' => $variavel,
                        'periodo' => $periodo,
                        'nome' => $nome,
                        'currentExpression' => $matches[0][$index],
                        'is_extenso' => $isExtenso,
                    ];
                } else {
                    $result[] = (object) [
                        'variavel' => $variavel,
                        'periodo' => $periodo,
                        'nome' => $nome,
                        'currentExpression' => $matches[0][$index],
                        'is_extenso' => $isExtenso,
                    ];

                    Log::debug('[SAPC] Variável de RREO8 não encontrada: '.$variavel);
                }
            }
        }

        return $result;
    }

    public function getVarsRREO($texto)
    {
        $result = [];
        if (preg_match_all('/\$_rreo(-([a-zA-Z0-9]+))?\{(.*?)\}/', $texto, $matches)) {
            foreach ($matches[3] as $index => $variavel) {
                $isExtenso = ($matches[2][$index] ?? null) === 'ext';

                [$periodo, $anexo, $cod_conta, $coluna] = explode('_', $variavel);

                $objVarRREO = SiconfiRREO::where([
                    'periodo' => $periodo,
                    'anexo' => $anexo,
                    'coluna' => $coluna,
                    'cod_conta' => $cod_conta,
                ])
                    ->first();

                if ($objVarRREO) {
                    $result[] = (object) [
                        'id' => $objVarRREO->id,
                        'variavel' => $variavel,
                        'periodo' => $periodo,
                        'anexo' => $anexo,
                        'coluna' => $coluna,
                        'cod_conta' => $cod_conta,
                        'currentExpression' => $matches[0][$index],
                        'is_extenso' => $isExtenso,
                    ];
                } else {
                    $result[] = (object) [
                        'variavel' => $variavel,
                        'periodo' => $periodo,
                        'anexo' => $anexo,
                        'coluna' => $coluna,
                        'cod_conta' => $cod_conta,
                        'currentExpression' => $matches[0][$index],
                        'is_extenso' => $isExtenso,
                    ];
                    Log::debug('[SAPC] Variável de RREO não encontrada: '.$variavel);
                }
            }
        }

        return $result;
    }

    public function getVarsRGF($texto)
    {
        $result = [];
        if (preg_match_all('/\$_rgf(-([a-zA-Z0-9<>]+))?\{(.*?)\}/', $texto, $matches)) {
            foreach ($matches[3] as $index => $variavel) {
                $isExtenso = ($matches[2][$index] ?? null) === 'ext';
                $variavel = html_entity_decode($variavel);
                [$poder, $periodo, $anexo, $cod_conta, $coluna] = explode('_', $variavel);

                $objVarRGF = SiconfiRGF::where([
                    'periodo' => $periodo,
                    'co_poder' => $poder,
                    'anexo' => $anexo,
                    'coluna' => $coluna,
                    'cod_conta' => $cod_conta,
                ])
                    ->first();

                if ($objVarRGF) {
                    $result[] = (object) [
                        'id' => $objVarRGF->id,
                        'variavel' => $variavel,
                        'currentExpression' => $matches[0][$index],
                        'periodo' => $periodo,
                        'poder' => $poder,
                        'anexo' => $anexo,
                        'cod_conta' => $cod_conta,
                        'coluna' => $coluna,
                        'is_extenso' => $isExtenso,
                    ];
                } else {
                    $result[] = (object) [
                        'variavel' => $variavel,
                        'currentExpression' => $matches[0][$index],
                        'periodo' => $periodo,
                        'poder' => $poder,
                        'anexo' => $anexo,
                        'cod_conta' => $cod_conta,
                        'coluna' => $coluna,
                        'is_extenso' => $isExtenso,
                    ];

                    Log::debug('[SAPC] Variável de RGF não encontrada: '.$variavel);
                }
            }
        }

        return $result;
    }

    public function getVarsElemento($texto)
    {
        // Defina o padrão da expressão regular
        $pattern = '/\$_elemento{(.*?)}/';

        preg_match_all($pattern, $texto, $matches);

        return $matches[1] ?? [];
    }

    public function getFormulas($texto)
    {
        // Defina o padrão da expressão regular
        $pattern = '/==#(.*?)#/';

        // Executa a correspondência usando preg_match_all
        if (preg_match_all($pattern, $texto, $matches)) {
            // $matches[1] agora conterá um array com os valores desejados
            return $matches[1];
        }

        return [];
    }

    public function parseTexto(CertidaoPVL $certidao, $texto, $exercicio, $cidade)
    {
        $codigoIbge = $certidao->esfera->is(Esfera::Estadual) ? 27 : $cidade->codigo_ibge;
        $parsedTexto = htmlspecialchars_decode($texto);

        $listaVariaveisRREO = $this->getVarsRREO($texto);

        foreach ($listaVariaveisRREO as $itemVariavel) {
            $currentExpression = $itemVariavel->currentExpression;

            $total = $this->calcularVariavelRREO(
                $exercicio,
                $codigoIbge,
                $itemVariavel
            );

            if (is_numeric($total)) {
                $parsedTexto = str_replace($currentExpression, number_format((float) $total, 2, ',', '.'), $parsedTexto);
            } else {
                $parsedTexto = str_replace($currentExpression, $total, $parsedTexto);
            }
        }

        $listaVariaveisRGF = $this->getVarsRGF($texto);
        foreach ($listaVariaveisRGF as $itemVariavel) {
            $currentExpression = $itemVariavel->currentExpression;

            $total = $this->calcularVariavelRGF(
                $exercicio,
                $codigoIbge,
                $itemVariavel
            );

            if (is_numeric($total)) {
                $parsedTexto = str_replace($currentExpression, number_format((float) $total, 2, ',', '.'), $parsedTexto);
                $parsedTexto = str_replace(html_entity_decode($currentExpression), number_format((float) $total, 2, ',', '.'), $parsedTexto);
            } else {
                $parsedTexto = str_replace($currentExpression, $total, $parsedTexto);
                $parsedTexto = str_replace(html_entity_decode($currentExpression), $total, $parsedTexto);
            }
        }

        $listaVariaveisRREO8Indicadores = $this->getVarsRREO8Indicadores($texto);
        foreach ($listaVariaveisRREO8Indicadores as $itemVariavel) {
            $currentExpression = $itemVariavel->currentExpression;

            $total = $this->calcularVariavelRREO8Indicadores(
                $exercicio,
                substr($codigoIbge, 0, -1),
                $itemVariavel
            );

            if (is_numeric($total)) {
                $parsedTexto = str_replace($currentExpression, number_format((float) $total, 2, ',', '.'), $parsedTexto);
            } else {
                $parsedTexto = str_replace($currentExpression, $total, $parsedTexto);
            }
        }

        $listaVariaveisRREO12 = $this->getVarsRREO12($texto);
        foreach ($listaVariaveisRREO12 as $itemVariavel) {
            $currentExpression = $itemVariavel->currentExpression;

            $total = $this->calcularVariavelRREO12(
                $exercicio,
                substr_replace($codigoIbge, '.', -1, 0),
                $itemVariavel
            );

            if (is_numeric($total)) {
                $parsedTexto = str_replace($currentExpression, number_format((float) $total, 2, ',', '.'), $parsedTexto);
            } else {
                $parsedTexto = str_replace($currentExpression, $total, $parsedTexto);
            }
        }

        $parsedTexto = $this->replaceVariaveisElemento($parsedTexto, $exercicio, $codigoIbge);
        $parsedTexto = $this->replaceVariaveisCertidao($parsedTexto, $certidao, $exercicio);

        // pegar todas as fórmulas e colocar no evaluate para calcular o seu valor, passando as variáveis calculadas anteriormente
        $listaFormulas = $this->getFormulas($parsedTexto);
        $language = new ExpressionLanguage;
        $language->register('date', function ($format) {
            // Essa parte é usada para gerar código PHP, se necessário
            return sprintf('date(%s)', $format);
        }, function ($arguments, $format) {
            // Aqui é onde a função é realmente executada
            return date($format);
        });

        foreach ($listaFormulas as $itemFormula) {
            try {

                preg_match_all('/\{([^}]+)\}/', $itemFormula, $matches);
                $variaveis = array_unique($matches[1]);

                foreach ($variaveis as $variavel) {
                    if (! isset($this->variaveis['{'.$variavel.'}'])) {
                        $this->variaveis['{'.$variavel.'}'] = 0;
                    }
                }

                $expressaoConvertida = $this->transformarExpressaoParaExpressionLanguage($itemFormula, $this->variaveis);
                $formulaEvaluated = $language->evaluate($expressaoConvertida, ['dados' => $this->variaveis]);

                if (is_float($formulaEvaluated)) {
                    $parsedTexto = str_replace('==#'.$itemFormula.'#', number_format((float) $formulaEvaluated, 2, ',', '.'), $parsedTexto);
                } else {
                    $parsedTexto = str_replace('==#'.$itemFormula.'#', $formulaEvaluated, $parsedTexto);
                }
            } catch (\Exception $e) {
                Log::debug('[SAPC] Erro ao avaliar a fórmula: '.$e->getMessage());
            }
        }

        return $parsedTexto;
    }

    public function replaceVariaveisCertidao($texto, $certidao, $exercicio)
    {
        $varPattern = '/\$_al(?:_Atual(-\d+)?)?\{(.*?)\}/';

        $texto = preg_replace_callback($varPattern, function ($matches) use ($certidao, $exercicio) {
            $variavel = $matches[2];

            if ($certidao->esfera->is(Esfera::Municipal)) {
                $unidadeGestora = UnidadeGestora::with('diretoria', 'gestor', 'controlador', 'responsavelTecnico')->where('cidade_id', $certidao->cidade_id)->where('tipo_unidade', TipoUnidade::Prefeitura)->where('subtipo', SubtipoUnidade::Prefeitura)->first();
            } else {
                $unidadeGestora = UnidadeGestora::with('diretoria', 'gestor', 'controlador', 'responsavelTecnico')->where('cidade_id', $certidao->cidade_id)->where('tipo_unidade', TipoUnidade::GovernoDoEstado)->where('subtipo', SubtipoUnidade::GovernoDoEstado)->first();
            }

            if ($variavel === 'Responsáveis Técnicos') {

                $nomes = $unidadeGestora->responsavelTecnico?->pluck('name')->toArray() ?? [];
                if (empty($nomes)) {
                    return 'N/A';
                }
                $ultimoNome = array_pop($nomes);

                return $nomes ? implode(', ', $nomes).' e '.$ultimoNome : $ultimoNome;
            }

            if ($variavel === 'Exercício') {
                $offset = empty($matches[1]) ? 0 : -(int) $matches[1];

                return $this->calcularAno($offset, (int) $exercicio);
            }

            $certidao->load('cidade');

            $variableMap = [
                'Modelo Análise' => $certidao->modeloAnalise->nome,
                'Município' => $certidao->cidade->nome,
                'Unidade Gestora' => $unidadeGestora->nome,
                'Data de criação' => $certidao->data_criacao->format('d/m/Y'),
                'Data de conclusão' => $certidao->data_conclusao?->format('d/m/Y'),
                'Assinatura' => $certidao->assinatura,
                'Status' => $certidao->status,
                'Diretoria' => $unidadeGestora->diretoria->nome,
                'Gestor' => $unidadeGestora->gestor->first()->name ?? null,
                'Controlador' => $unidadeGestora->controlador->first()->name ?? null,
                'População' => $certidao->cidade->populacao,
                'CNPJ' => preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $unidadeGestora->cnpj),
            ];

            return $variableMap[$variavel];
        }, $texto);

        $varPattern = '/\$_crt(-([a-zA-Z0-9<>]+))?\{(.*?)\}/';
        $texto = preg_replace_callback($varPattern, function ($matches) use ($certidao) {
            $variavel = $matches[3];

            $variableMap = [
                'declaracao_instituicao' => ($certidao->declaracao_instituicao === true) ? 1 : 0,
                'declaracao_limites' => ($certidao->declaracao_limites === true) ? 1 : 0,
                'declaracao_operacoes' => $certidao->declaracao_operacoes === true ? 1 : 0,
            ];

            return $variableMap[$variavel];
        }, $texto);

        $this->variaveis['declaracao_instituicao'] = ($certidao->declaracao_instituicao === true) ? 1 : 0;
        $this->variaveis['declaracao_limites'] = ($certidao->declaracao_limites === true) ? 1 : 0;
        $this->variaveis['declaracao_operacoes'] = $certidao->declaracao_operacoes === true ? 1 : 0;

        return $texto;
    }

    public function replaceVariaveisElemento($texto, $exercicio, $codigoIbge)
    {
        $listas = $this->getVarsElemento($texto);

        foreach ($listas as $itemLista) {
            $modeloElemento = ModeloElemento::where('variavel', $itemLista)->first();

            if (! empty($modeloElemento->consulta)) {
                $consulta = str_replace('${exercicio}', $exercicio, $modeloElemento->consulta);
                $consulta = str_replace('${codigoIbge}', $codigoIbge, $consulta);

                $resultadoConsulta = eval('return '.$consulta.';');

                $dataObj = $resultadoConsulta
                    // ->where('exercicio', $exercicio)
                    // ->where('cod_ibge', $codigoIbge)
                    ->first();

                if (empty($dataObj)) {
                    $valorColuna = '';
                } else {
                    $valorColuna = $dataObj->{$modeloElemento->coluna};
                }

                if (str_starts_with($modeloElemento->coluna, 'data')) {
                    $valorColuna = Carbon::parse($valorColuna)->format('d/m/Y');
                }

                if (str_starts_with($modeloElemento->coluna, 'valor')) {
                    $valorColuna = number_format((float) $valorColuna, 2, ',', '.');
                }

                $texto = str_replace('$_elemento{'.$itemLista.'}', $valorColuna, $texto);
            }
        }

        return $texto;
    }

    public function restaurarVersao(CertidaoPVLFormularioVersao $versaoARestaurar, $certidaoPVLFormulario)
    {
        try {
            $versaoAtual = CertidaoPVLFormulario::query()
                ->find($versaoARestaurar->certidao_formulario_id);

            CertidaoPVLFormularioVersao::query()->create([
                'versao' => $versaoAtual->versao,
                'certidao_formulario_id' => $versaoAtual->id,
                'data_preenchimento' => $versaoAtual->data_preenchimento,
                'texto' => $versaoAtual->texto,
                'user_id' => $versaoAtual->user_id,
            ]);

            $versaoAtual->fill([
                'user_id' => $versaoARestaurar->user_id,
                'versao' => ++$versaoAtual->versao,
                'data_preenchimento' => $versaoARestaurar->data_preenchimento,
                'texto' => $versaoARestaurar->texto,
            ])->saveQuietly();

            $this->logAcao($certidaoPVLFormulario, CertidaoFormularioHistoricoAcao::RestaurarVersao, $versaoARestaurar->versao);

            return $versaoAtual;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function listarVersoes(CertidaoPVLFormulario $certidaoPVLFormulario)
    {
        return $certidaoPVLFormulario
            ->versoes()
            ->with('user')
            ->paginate(request()->input('itemsPerPage', 20));
    }

    public function logAcao(CertidaoPVLFormulario $formulario, string $acao, string $valor)
    {
        return CertidaoPVLFormularioHistoricoAcao::create([
            'certidao_formulario_id' => $formulario->id,
            'acao' => $acao,
            'valor' => $valor,
            'user_id' => auth()->user()->id,
            'data_acao' => now(),
        ]);
    }

    public function getAcoes(CertidaoPVLFormulario $formulario)
    {
        return CertidaoPVLFormularioHistoricoAcao::query()
            ->with('user')
            ->where('certidao_formulario_id', $formulario->id)
            ->orderBy('id', 'desc')
            ->paginate(request()->input('itemsPerPage'));
    }

    public function downloadFormulariosCertidao(CertidaoPVL $certidao)
    {
        try {
            $certidao->load(['cidade', 'formularios' => function ($query) {
                $query->join('sapc.modelo_formulario', 'sapc.certidao_pvl_formularios.modelo_formulario_id', '=', 'sapc.modelo_formulario.id')
                    ->join('sapc.modelo_analise_formulario', 'sapc.modelo_formulario.id', '=', 'sapc.modelo_analise_formulario.modelo_formulario_id')
                    ->orderBy('sapc.modelo_analise_formulario.ordenacao', 'ASC')
                    ->select('sapc.certidao_pvl_formularios.*');
            }]);

            $cabecalhoPadrao = Parametrizacao::getValor('cabecalho_padrao');

            $formsContentHtml = view('pdf.certidoes.analise.formularios', ['analise' => $certidao, 'formularios' => $certidao->formularios, 'cabecalhoPadrao' => $cabecalhoPadrao])->render();
            $pdfFileName = "formulario_{$certidao->id}.pdf";

            $sapcPdfRenderStyles = public_path('/sapc/pdf/render/sapc-pdf-render.css');
            $sapcPdfRenderScript = public_path('/sapc/pdf/render/create-toc.js');

            $rawPdf = \Spatie\Browsershot\Browsershot::html($formsContentHtml)
                ->newHeadless()
                ->noSandbox()
                ->waitUntilNetworkIdle()
                ->delay(3000)
                ->showBackground()
                ->emulateMedia(self::PDF_MEDIA)
                ->format(self::PDF_FORMAT)
                ->margins(self::PDF_TOP_MARGIN, self::PDF_RIGHT_MARGIN, self::PDF_BOTTOM_MARGIN, self::PDF_LEFT_MARGIN)
                ->setOption('addStyleTag', json_encode(['path' => $sapcPdfRenderStyles]))
                ->setOption('addScriptTag', json_encode(['path' => $sapcPdfRenderScript]))
                ->timeout(60 * 5)
                ->pdf();

            $data = [
                'name' => $pdfFileName,
                'pdf' => $rawPdf,
            ];

            return (object) $data;
        } catch (Exception $e) {
            // ToDo: return an error page
            Log::error('Erro ao gerar o PDF: '.$e->getMessage());

            return null;
        }
    }

    public function storeCertidaoPVL(CertidaoPVLFormulario $certidaoPVLFormulario, array $data)
    {
        // GERAÇÃO DE NOVA VERSAO
        CertidaoPVLFormularioVersao::create([
            'versao' => $certidaoPVLFormulario->versao,
            'texto' => $certidaoPVLFormulario->texto,
            'data_preenchimento' => $certidaoPVLFormulario->data_preenchimento ?? now(),
            'certidao_formulario_id' => $certidaoPVLFormulario->id,
            'user_id' => $certidaoPVLFormulario->user_id,
        ]);
        $version = ($certidaoPVLFormulario->versao ?? 0) + 1;
        $certidaoPVLFormulario->versao = $version;

        $certidaoPVLFormulario->status = $certidaoPVLFormulario->status == CertidaoFormularioStatus::FINALIZADO ? $certidaoPVLFormulario->status : $data['status'];
        $certidaoPVLFormulario->texto = $data['texto'];
        $certidaoPVLFormulario->data_preenchimento = now();
        $certidaoPVLFormulario->user_id = Auth::id();
        $certidaoPVLFormulario->texto_auto_save = null;
        $certidaoPVLFormulario->save();

        $certidao = CertidaoPVL::find($certidaoPVLFormulario->certidao_id);

        $isCertidaoConcluida = $certidao->formularios()
            ->where('status', '<>', CertidaoFormularioStatus::FINALIZADO)
            ->doesntExist();

        $certidaoStatus = $isCertidaoConcluida ? CertidaoStatusEnum::CONCLUIDA : CertidaoStatusEnum::EM_ANDAMENTO;

        $certidao->update(['status' => $certidaoStatus]);

        if ($certidaoPVLFormulario->status === CertidaoFormularioStatus::FINALIZADO) {
            $this->logAcao($certidaoPVLFormulario, CertidaoFormularioHistoricoAcao::FinalizarFormulario, $certidaoPVLFormulario->versao);
        } else {
            $this->logAcao($certidaoPVLFormulario, CertidaoFormularioHistoricoAcao::SalvarFormulario, $certidaoPVLFormulario->versao);
        }

        return $certidaoPVLFormulario;
    }

    public function autoSaveCertidao($certidaoPVLFormulario, $text)
    {
        $certidaoPVLFormulario
            ->update(['texto_auto_save' => $text]);
    }

    public function calcularAno($numero, $exercicio)
    {

        if (empty($numero)) {
            return (int) $exercicio;
        }

        return $exercicio - (int) $numero;
    }

    public function recalcularCertidao($certidao)
    {
        $formularioRecalculado = [
            'status' => CertidaoFormularioStatus::AGUARDANDO_INICIO,
        ];
        $cidade = $certidao->cidade;
        $formulariosRecalculados = [];

        foreach ($certidao->formularios as $certidaoPVLFormularioAtual) {
            $novoModeloFormulario = ModeloFormulario::find($certidaoPVLFormularioAtual->modelo_formulario_id);

            $oldContent = $this->parseTexto(
                $certidao,
                $certidaoPVLFormularioAtual->texto,
                $certidaoPVLFormularioAtual->exercicio,
                $cidade
            );

            $newContent = $this->parseTexto(
                $certidao,
                $novoModeloFormulario->texto,
                $certidaoPVLFormularioAtual->exercicio,
                $cidade
            );

            if (($newContent === $oldContent) && (! $certidaoPVLFormularioAtual->status->is(CertidaoFormularioStatus::FINALIZADO))) {
                continue;
            }

            $formularioRecalculado['texto'] = $newContent;
            $formulariosRecalculados[] = $this->storeCertidaoPVL($certidaoPVLFormularioAtual, $formularioRecalculado);
        }

        return $formulariosRecalculados;
    }

    public function fetchCertidoes(Request $data)
    {
        $queryBuilder = CertidaoPVL::query()
            ->with(['modeloAnalise', 'cidade', 'user']);

        if ($data->filled('data_inicio') && $data->filled('data_fim')) {
            $dataInicio = \Carbon\Carbon::parse($data->input('data_inicio'))->startOfDay();
            $dataFim = \Carbon\Carbon::parse($data->input('data_fim'))->endOfDay();

            $queryBuilder->whereBetween('data_criacao', [$dataInicio, $dataFim]);
        }

        if (! is_null($data->input('esfera'))) {
            $esferas = array_map(fn ($value) => Esfera::fromValue((int) $value)->value, (array) $data->input('esfera'));
            $queryBuilder->whereIn('esfera', $esferas);
        }

        if (! is_null($data->input('municipio'))) {
            $ugIds = (array) $data->input('municipio');
            $queryBuilder->whereHas('cidade.unidades', function ($query) use ($ugIds) {
                $query->whereIn('id', $ugIds);
            });
        }

        if (! is_null($data->input('situacao'))) {
            $situacoes = (array) $data->input('situacao');
            $queryBuilder->whereIn('status', $situacoes);
        }

        $queryBuilder->orderBy('sapc.certidao_pvl.created_at', 'desc');

        return $queryBuilder->paginate($data->input('itemsPerPage', 20));
    }

    public function listarCertidaoFormularios($certidaoId)
    {
        return CertidaoPVLFormulario::select('sapc.certidao_pvl_formularios.*')
            ->with(['certidao', 'modeloFormulario', 'user'])
            ->join('sapc.certidao_pvl', 'sapc.certidao_pvl.id', '=', 'sapc.certidao_pvl_formularios.certidao_id')
            ->join('sapc.modelo_analise_formulario as maf', function ($join) {
                $join->on('maf.modelo_formulario_id', '=', 'sapc.certidao_pvl_formularios.modelo_formulario_id')
                    ->on('maf.modelo_analise_id', '=', 'sapc.certidao_pvl.modelo_analise_id');
            })

            ->where('sapc.certidao_pvl.id', $certidaoId)
            ->orderBy('maf.ordenacao')
            ->orderBy('exercicio')
            ->get();
    }

    public function formularioReabrir(CertidaoPVL $certidao, CertidaoPVLFormulario $certidaoPVLFormulario)
    {
        if ($certidaoPVLFormulario->status->value !== CertidaoFormularioStatus::FINALIZADO) {
            throw new ModelNotFoundException('Formulario não está finalizado');
        }

        $certidaoPVLFormulario->status = CertidaoFormularioStatus::REABERTO;
        $certidaoPVLFormulario->save();
        $this->logAcao($certidaoPVLFormulario, CertidaoFormularioHistoricoAcao::ReabrirFormulario, $certidaoPVLFormulario->versao);

        $certidao->update(['status' => CertidaoStatusEnum::EM_ANDAMENTO]);

        return $certidaoPVLFormulario;
    }

    public function setStatusEmitida(CertidaoPVL $certidao)
    {
        $certidao->status = CertidaoStatusEnum::EMITIDA;
        $certidao->save();

        return $this->downloadFormulariosCertidao($certidao);
    }

    public function atualizarOficio(CertidaoPVL $certidao, string $oficio)
    {
        $certidao->oficio = $oficio;
        $certidao->save();
    }

    public function transformarExpressaoParaExpressionLanguage(string $expressao, array $dados): string
    {
        $variaveis = array_keys($dados);

        foreach ($variaveis as $nomeVar) {
            $expressao = str_replace($nomeVar, "dados['".$nomeVar."']", $expressao);
        }

        return $expressao;
    }
}
