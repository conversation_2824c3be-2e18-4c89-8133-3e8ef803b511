<?php

namespace App\Services;

use App\Enums\Comportamento;
use App\Enums\Sapc\CertidaoStatusEnum;
use App\Models\Cidade;
use App\Enums\GrupoRegional;
use App\Enums\Esfera;
use App\Enums\NaturezaLancamento;
use App\Enums\PeriodoRemessaStatusEnum;
use App\Enums\SentStatusQuestionario;
use App\Enums\TipoAdministracao;
use App\Models\Diretoria;
use App\Enums\SubtipoUnidade as EnumsSubtipoUnidade;
use App\Enums\TipoUnidade as EnumsTipoUnidade;
use App\Models\PeriodoQuestionario;
use App\Models\PeriodoRemessa;
use App\Models\Sicap\Exercicio2022\TabelasAuxiliares\PoderOrgao;
use App\Models\SubtipoUnidade;
use App\Models\TipoLayout;
use App\Models\TipoQuestionario;
use App\Models\TipoUnidade;
use App\Models\UnidadeGestora;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Fluent;
use Illuminate\Support\Str;

use App\Traits\Utils;

class ApiService
{
    use Utils;

    public function fetchUnidadesGestoras(Request $request): ?LengthAwarePaginator
    {
        $user = auth()->user();
        $sortBy = $request['sortBy'][0] ?? 'nome';
        $sortDesc = $request->input('sortDesc') == (array) 'true' ? 'DESC' : 'ASC';

        return $user?->unidadesGestoras()
            ->with(['jurisdicionado', 'orgao', 'cidade', 'unidadesGestorasSubsidiarias'])
            ->filters($request)
            ->orderBy($sortBy, $sortDesc)
            ->paginate($request->input('itemsPerPage', 5));
    }

    public function fetchAllUnidadesGestoras(Request $request): LengthAwarePaginator
    {
        $sortBy = $request['sortBy'][0] ?? 'nome';
        $sortDesc = $request->input('sortDesc') == (array) 'true' ? 'DESC' : 'ASC';

        return UnidadeGestora::query()
            ->with(['jurisdicionado', 'orgao', 'cidade', 'unidadesGestorasSubsidiarias'])
            ->filters($request)
            ->orderBy($sortBy, $sortDesc)
            ->paginate($request->input('itemsPerPage', 5));
    }

    public function fetchTiposUnidade(): Collection
    {
        return TipoUnidade::all(['id', 'description']);
    }

    public function fetchMunicipio(array $filtros): Collection
    {
        return Cidade::orderBy('nome')
            ->select('id', 'nome')
            ->when(isset($filtros['nome']), fn ($q) => $q->where('nome', 'ilike', "%{$filtros['nome']}%"))
            ->get();
    }

    public function fetchRelatorias(): Collection
    {
        return collect(GrupoRegional::asSelectArray())
            ->map(fn ($grupo, $key) => (new Fluent([
                'key' => $key,
                'value' => $grupo,
            ])));
    }

    public function fetchEsferas(): Collection
    {
        return collect(Esfera::asSelectArray())
            ->map(fn ($value, $key) => (new Fluent([
                'key' => $key,
                'value' => $value,
            ])));
    }

    public function fetchExercicios(): Collection
    {
        return PeriodoRemessa::distinct('exercicio')
            ->orderBy('exercicio')
            ->get('exercicio');
    }

    public function fetchPoderes(): Collection
    {
        return PoderOrgao::orderBy('descricao')
            ->select('codigo', 'descricao')
            ->get();
    }

    public function fetchQuestionarioTipos(): Collection
    {
        return TipoQuestionario::select(['id', 'sigla'])
            ->orderBy('sigla')
            ->get();
    }

    public function fetchQuestionarioStatus(): Collection
    {
        return collect(SentStatusQuestionario::asSelectArray())
            ->map(fn ($value, $key) => (new Fluent([
                'key' => $key,
                'value' => $value,
            ])));
    }

    public function fetchGruposRegionais(): Collection
    {
        return collect(GrupoRegional::asSelectArray())
            ->map(fn ($grupo, $key) => (new Fluent([
                'key' => $key,
                'value' => $grupo,
            ])));
    }

    public function fetchTiposAdministracao(): Collection
    {
        return collect(TipoAdministracao::asSelectArray())
            ->map(fn ($value, $key) => (new Fluent([
                'key' => $key,
                'value' => $value,
            ])));
    }

    public function fetchSubtiposUnidades(): Collection
    {
        return SubtipoUnidade::orderBy('descricao')
            ->select('id', 'descricao')
            ->get();
    }

    public function fetchDiretorias(): Collection
    {
        return Diretoria::orderBy('sigla')
            ->select(['id', 'sigla'])
            ->get();
    }

    public function fetchComportamentos(): Collection
    {
        return collect(Comportamento::asSelectArray())
            ->map(fn ($comportamento, $key) => (new Fluent([
                'key' => $key,
                'value' => $comportamento,
            ])));
    }

    public function fetchPeriodosRemessa(Request $request): Collection
    {
        $sortBy = $request['sortBy'][0] ?? 'nome';
        $sortDesc = $request->input('sortDesc') == (array) 'true' ? 'DESC' : 'ASC';

        return PeriodoRemessa::query()
            ->when(
                $request->filled('nome'),
                fn ($query) => $query
                    ->whereRaw('nome ILIKE ?', ['%'.$request->input('nome').'%'])
            )
            ->orderBy($sortBy, $sortDesc)
            ->get();
    }

    public function fetchTiposLayouts(Request $request): LengthAwarePaginator
    {
        $sortBy = $request['sortBy'][0] ?? 'nome';
        $sortDesc = $request->input('sortDesc') == (array) 'true' ? 'DESC' : 'ASC';

        return TipoLayout::query()
            ->when($request->filled('nome'), fn ($q) => $q
                ->where('nome', 'ilike', "%{$request->input('nome')}%"))
            ->orderBy($sortBy, $sortDesc)
            ->paginate($request->input('itemsPerPage', 5));
    }

    public function fetchNaturezasLancamento(Request $request): Collection
    {
        return collect(NaturezaLancamento::toVueArray())
            ->when(
                $request->filled('nome'),
                fn ($collection) => $collection
                    ->filter(
                        fn ($array) => Str::contains(
                            $array['text'],
                            $request->input('nome'),
                            true
                        )
                    )
            );
    }

    public function fetchPeriodoRemessaStatus(): Collection
    {
        return PeriodoRemessaStatusEnum::getAllDescriptions();
    }

    public function fetchQuestionarioExercicios(): Collection
    {
        return PeriodoQuestionario::query()
            ->distinct()
            ->orderBy('exercicio')
            ->get(['exercicio as text', 'exercicio as value']);
    }

    public function fetchCertidaoSituacoes(): Collection
    {
        return collect(CertidaoStatusEnum::asSelectArray())
            ->map(fn ($situacao, $key) => (new Fluent([
                'text' => $situacao,
                'value' => $key,
            ])))->values();
    }

    public function fetchCertidaoMunicipios() 
    {
        $ugGoverno = UnidadeGestora::where([
            'tipo_unidade' => EnumsTipoUnidade::GovernoDoEstado,
            'subtipo' => EnumsSubtipoUnidade::GovernoDoEstado
        ])
        ->pluck('id')
        ->map(fn($id) => [
            'value' => $id, 
            'text' => 'Governo do Estado'
        ]);
    
        $ugPrefeituras = UnidadeGestora::where(column: [
            'tipo_unidade' => EnumsTipoUnidade::Prefeitura,
            'subtipo' => EnumsSubtipoUnidade::Prefeitura
        ])
        ->get()
        ->map(fn($item)=>[
            'value' => $item->id, 
            'text' => "{$item->cidade->nome} ({$this->formatar_cpf_cnpj($item->cnpj)})"
        ])
        ->sortBy(fn($item) => Str::ascii($item['text']));

        return $ugGoverno->concat($ugPrefeituras)->toArray();
    }
}