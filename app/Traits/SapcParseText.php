<?php

namespace App\Traits;

use App\Helpers\ExpressionVariableExtractor;
use App\Models\Sapc\ModeloLista;
use App\Models\Sapc\VariavelContaGestao;
use App\Models\Sapc\VariavelContaGoverno;
use App\Models\Sicap\Exercicio2022\LeisOrcamentarias;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

trait SapcParseText
{
    protected $listaVariaveisGovernoByNome = [];

    public function getVarsGestao($texto)
    {
        // Defina o padrão da expressão regular
        $pattern = '/\$_ge{(.*?)}/';

        preg_match_all($pattern, $texto, $matches);

        return $matches[1] ?? [];
    }

    public function getVarsLista($texto)
    {
        // Defina o padrão da expressão regular
        $pattern = '/\$_lista{(.*?)}/';

        preg_match_all($pattern, $texto, $matches);

        return $matches[1] ?? [];
    }

    public function getFormulas($texto)
    {
        // Defina o padrão da expressão regular
        $pattern = '/==#(.*?)#/';

        // Executa a correspondência usando preg_match_all
        if (preg_match_all($pattern, $texto, $matches)) {
            // $matches[1] agora conterá um array com os valores desejados
            return $matches[1];
        }

        return [];
    }

    public function calcularVariavelContaGestao(VariavelContaGestao $variavelContaGestao, int $ug, int $exercicio)
    {
        $grupos = $variavelContaGestao->grupos->sortBy(fn ($item) => $item->ordem);
        $agrupamento = $variavelContaGestao->agrupamentoCampoLayout;
        $agrupamentoCampo = ! is_null($agrupamento) ? ltrim(strtolower(preg_replace('/[A-Z]/', '_$0', $agrupamento->campo)), '_') : null;
        $resultado = ! is_null($agrupamento) ? [] : 0;

        foreach ($grupos as $grupo) {
            $termos = $grupo->termos;
            $resultadoGrupo = ! is_null($agrupamento) ? [] : 0;

            foreach ($termos as $termo) {
                $campoLayout = $termo->campoLayout;
                $layout = $termo->layout;

                $tabela = ltrim(strtolower(preg_replace('/[A-Z]/', '_$0', $layout->nome)), '_');
                $campo = ltrim(strtolower(preg_replace('/[A-Z]/', '_$0', $campoLayout->campo)), '_');

                $items = DB::table("exercicio_2024.sicap_{$tabela}")
                    ->select("exercicio_2024.sicap_{$tabela}.*")
                    ->join('remessa_parcials as rp', 'rp.id', '=', "exercicio_2024.sicap_{$tabela}.remessa_parcial_id")
                    ->join('remessas as r', 'r.id', '=', 'rp.remessa_id')
                    ->join('unidade_gestoras as ug', 'ug.id', '=', 'r.unidade_gestora_id')
                    ->join('periodo_remessas as pr', 'pr.id', '=', 'r.periodo_remessa_id')
                    ->where('ug.id', '=', $ug)
                    ->where('pr.exercicio', '=', $exercicio)
                    ->get();

                foreach ($items as $item) {
                    if ($termo->operador === '+') {
                        if (is_null($agrupamento)) {
                            $resultadoGrupo += (int) $item->{$campo};
                        } else {
                            if (! isset($resultadoGrupo[$item->{$agrupamentoCampo}])) {
                                $resultadoGrupo[$item->{$agrupamentoCampo}] = 0;
                            }

                            $resultadoGrupo[$item->{$agrupamentoCampo}] += (int) $item->{$campo};
                        }
                    } else {
                        if (is_null($agrupamento)) {
                            $resultadoGrupo -= (int) $item->{$campo};
                        } else {
                            if (! isset($resultadoGrupo[$item->{$agrupamentoCampo}])) {
                                $resultadoGrupo[$item->{$agrupamentoCampo}] = 0;
                            }

                            $resultadoGrupo[$item->{$agrupamentoCampo}] -= (int) $item->{$campo};
                        }
                    }
                }
            }

            if ($grupo->operador === '+') {
                if (is_null($agrupamento)) {
                    $resultado += $resultadoGrupo;
                } else {
                    foreach ($resultadoGrupo as $key => $result) {
                        if (! isset($resultado[$key])) {
                            $resultado[$key] = 0;
                        }

                        $resultado[$key] += $result;
                    }
                }
            } else {
                if (is_null($agrupamento)) {
                    $resultado -= $resultadoGrupo;
                } else {
                    foreach ($resultadoGrupo as $key => $result) {
                        if (! isset($resultado[$key])) {
                            $resultado[$key] = 0;
                        }

                        $resultado[$key] -= $result;
                    }
                }
            }
        }

        return $resultado;
    }

    public function getVarsGoverno($texto, $exercicio)
    {
        $pattern = '/\$_go_Atual(-(\d+))?\_\{(.*?)\}/';
        $result = [];

        if (preg_match_all($pattern, $texto, $matches)) {
            foreach ($matches[3] as $index => $variavel) {

                $numero = $matches[2][$index] ?? null;

                if ($numero == '0') {
                    if (isset($this->variaveis[$variavel])) {
                        $result[] = (object) [
                            'numero' => 0,
                            'exercicio' => $exercicio,
                            'variavel' => $variavel,
                            'currentExpression' => '$_go_Atual_{'.$variavel.'}',
                        ];

                        continue;
                    }
                }

                $objVarGoverno = $this->listaVariaveisGovernoByNome[$variavel] ?? null;

                if ($objVarGoverno) {
                    $result[] = (object) [
                        'id' => $objVarGoverno->id,
                        'numero' => $numero,
                        'exercicio' => $this->calcularAno($numero, $exercicio),
                        'variavel' => $variavel,
                        'currentExpression' => $matches[0][$index],
                        'periodo_inicial' => $objVarGoverno->periodo_inicial,
                        'periodo_final' => $objVarGoverno->periodo_final,
                    ];

                } else {
                    Log::debug('[SAPC] Variável de Governo não encontrada: '.$variavel);
                }
            }
        }

        return $result;
    }

    public function calcularAno($numero, $exercicio)
    {

        if (empty($numero)) {
            return (int) $exercicio;
        }

        return $exercicio - (int) $numero;
    }

    public function replaceVariaveisGoverno($texto, $analise, $listaVariaveisGovernoByNome = [])
    {
        $this->listaVariaveisGovernoByNome = $listaVariaveisGovernoByNome;
        $listaVariaveisGoverno = $this->getVarsGoverno($texto, $analise->exercicio);
        foreach ($listaVariaveisGoverno as $itemVariavel) {
            $currentExpression = $itemVariavel->currentExpression;

            if ($itemVariavel->numero != '0' && $itemVariavel->numero != '1' && $itemVariavel->numero != '') {
                $unidadeGestoraIds = $analise->unidadeGestora->unidadesGestorasSubsidiarias->pluck('id')->toArray();
                $unidadeGestoraIds[] = $analise->unidade_gestora_id;

                foreach ($unidadeGestoraIds as $key => $unidadeGestoraId) {
                    $matriz = $this->buscarMatriz($unidadeGestoraId, $itemVariavel->exercicio);
                    $total = $this->calcularVariavel($itemVariavel, $matriz);
                    $this->variaveis[$itemVariavel->variavel.'__'.$itemVariavel->numero] = ($this->variaveis[$itemVariavel->variavel.'__'.$itemVariavel->numero] ?? 0) + $total;
                }
            } else {
                if ($itemVariavel->numero != '0' && $itemVariavel->numero != '') {
                    $total = $this->variaveis[$itemVariavel->variavel.'__'.$itemVariavel->numero];
                } else {
                    $total = $this->variaveis[$itemVariavel->variavel];
                }
            }

            if (is_numeric($total)) {
                $texto = str_replace($currentExpression, number_format((float) $total, 2, ',', '.'), $texto);
            } else {
                $texto = str_replace($currentExpression, $total, $texto);
            }
        }

        return $texto;
    }

    public function replaceVariaveisGestao($texto, $analise)
    {
        $listaVariaveisGestao = $this->getVarsGestao($texto);
        foreach ($listaVariaveisGestao as $itemVar) {
            $varGestao = VariavelContaGestao::where('nome', $itemVar)->first();
            if ($varGestao) {
                $resultVariavelContaGestao = $this->calcularVariavelContaGestao($varGestao, $analise->unidade_gestora_id, $analise->exercicio);
                $this->variaveis[$itemVar] = $resultVariavelContaGestao;

                if (isset($this->variavel[$itemVar][1])) {
                    $texto = str_replace('$_ge{'.$itemVar.'}', $this->variaveis[$itemVar][1], $texto);
                }

                if (isset($parsedVars[$itemVar][1])) {
                    $texto = str_replace('$_ge{'.$itemVar.'}', $parsedVars[$itemVar][1], $texto);
                }
            }
        }

        return $texto;
    }

    public function replaceVariaveisLista($texto, $analise)
    {
        $listas = $this->getVarsLista($texto);
        foreach ($listas as $itemLista) {
            $modeloLista = ModeloLista::where('variavel', $itemLista)->first();
            $textoTabela = $modeloLista->html;

            if ($modeloLista->has_percentual) {
                $textoTabela = str_replace('</colgroup>', '<col style="width:10%;"></colgroup>', $textoTabela);
                $textoTabela = str_replace('</tr></tbody>', '<td style="background-color:#BDD6EE;border:1px solid hsl(0, 0%, 60%);text-align:center;"><span class="text-small"><strong>'.$modeloLista->nome_percentual.'</strong></span></td></tr></tbody>', $textoTabela);
            }

            if (! empty($modeloLista->consulta)) {
                $resultadoConsulta = eval('return '.$modeloLista->consulta.';');

                $dataList = $resultadoConsulta
                    ->where('public.periodo_remessas.exercicio', $analise->exercicio)
                    ->where('public.remessas.unidade_gestora_id', $analise->unidade_gestora_id)
                    ->get();
                $colunas = explode(',', $modeloLista->colunas);

                if (! $dataList->isEmpty()) {
                    $dataColTotal = '0';
                    $dataDivisorTotal = '0';
                    $formula = $this->getFormulas($modeloLista->calculo_total_percentual);

                    $expressao = $formula[0] ?? null;

                    if ($expressao) {
                        $language = new ExpressionLanguage;
                        $extractor = new ExpressionVariableExtractor;
                        $variaveis = $extractor->getVariablesFromExpression($expressao);
                        // dump($variaveis);
                        $totalVariaveis = [];
                        foreach ($variaveis as $key => $variavel) {
                            $itemVariavel = VariavelContaGoverno::where('nome', $variavel)->first();
                            if ($itemVariavel) {
                                $matriz = $this->buscarMatriz($analise->unidade_gestora_id, $analise->exercicio, $itemVariavel);
                                $totalVariaveis[$variavel] = $this->calcularVariavel($itemVariavel, $matriz);
                            } else {
                                Log::debug('[SAPC] Variável de Governo não encontrada: '.$variavel);
                            }
                        }
                        $dataDivisorTotal = $language->evaluate($expressao, $totalVariaveis);
                        // dump($dataDivisorTotal);
                    }

                    $tableData = [];
                    // gera um array com todas as linhas preparadas e calculadas para a geração da tabela
                    foreach ($dataList as $key => $dataObj) {
                        $tableData[$key] = [];
                        foreach ($colunas as $coluna) {
                            $valorColuna = $dataObj->{$coluna};
                            if ($coluna == $modeloLista->coluna_total) {
                                $dataColTotal = bcadd($dataColTotal, $valorColuna);
                            }

                            if (str_starts_with($coluna, 'data')) {
                                $valorColuna = Carbon::parse($valorColuna)->format('d/m/Y');
                            }
                            if (str_starts_with($coluna, 'valor')) {
                                $valorColuna = number_format((float) $valorColuna, 2, ',', '.');
                            }
                            $tableData[$key][$coluna] = $valorColuna;
                        }
                    }

                    // Se tiver que calcular percentual
                    if ($modeloLista->has_percentual) {
                        if ($dataDivisorTotal == 0) {
                            $dataDivisorTotal = $dataColTotal;
                        }
                        foreach ($dataList as $key => $dataObj) {
                            $tableData[$key]['percentual'] = bcmul(bcdiv(str_replace(['.', ','], ['', '.'], $tableData[$key][$modeloLista->coluna_total]), $dataDivisorTotal, 8), 100, 2);
                        }
                    }
                    // fim do calculo percentual

                    foreach ($tableData as $tableLine) {
                        $newRow = '<tr>';
                        foreach ($tableLine as $tableKey => $tableColumn) {
                            $tableColumn = $tableKey == 'percentual' ? $tableColumn.'%' : $tableColumn; // se tiver que calcular percentual

                            $textAlign = '';
                            if (str_starts_with($tableKey, 'valor') || $tableKey == 'percentual') {
                                $textAlign = 'text-align:right;';
                            }

                            $newRow .= '<td style="height:15px;'.$textAlign.'"><span class="text-small">'.$tableColumn.'</span></td>';
                        }

                        $newRow .= '</tr>';
                        $textoTabela = str_replace('</tbody>', $newRow.'</tbody>', $textoTabela);
                    }

                    // Linha do total
                    \Log::info('Linha do total');
                    \Log::info('Total da Coluna '.$dataColTotal);
                    \Log::info('Total do Divisor '.$dataDivisorTotal);

                    if ($dataColTotal && $modeloLista->coluna_total) {
                        $newRow = '<tr>';
                        foreach ($colunas as $key => $coluna) {
                            if ($key == 0) {
                                $newRow .= '<td style="background-color: #bdd6ee; border: 1px solid hsl(0, 0%, 60%); text-align: center;"><strong><span class="text-small">Total</span></strong></td>';
                            } else {
                                if ($coluna == $modeloLista->coluna_total) {
                                    $newRow .= '<td style="background-color: #bdd6ee; border: 1px solid hsl(0, 0%, 60%); text-align: right;"><span class="text-small"><strong>'.number_format((float) $dataColTotal, 2, ',', '.').'</strong></span></td>';
                                } else {
                                    $newRow .= '<td style="background-color: #bdd6ee; border: 1px solid hsl(0, 0%, 60%); text-align: center;">&nbsp</td>';
                                }
                            }
                        }
                        if ($modeloLista->has_percentual) {
                            $newRow .= '<td style="background-color: #bdd6ee; border: 1px solid hsl(0, 0%, 60%); text-align: right;"><span class="text-small"><strong>'.number_format((float) (($dataColTotal / $dataDivisorTotal) * 100), 2, ',', '.').'%</strong></span></td>';
                        }
                        $newRow .= '</tr>';
                        $textoTabela = str_replace('</tbody>', $newRow.'</tbody>', $textoTabela);
                    }
                    // fim linha do total

                } else {
                    $newRow = '<tr>';
                    foreach ($colunas as $coluna) {
                        $newRow .= '<td></td>';
                    }
                    $newRow .= '</tr>';
                    $textoTabela = str_replace('</tbody>', $newRow.'</tbody>', $textoTabela);
                }
            }

            $texto = str_replace('$_lista{'.$itemLista.'}', $textoTabela, $texto);
        }

        return $texto;
    }

    public function replaceVariaveisLeisOrcamentarias($texto, $analise)
    {
        $varPattern = '/\$_lo(?:_Atual(-\d+)?)?\{(.*?)\}/';

        if (preg_match($varPattern, $texto)) {
            $leiOrcamentaria = LeisOrcamentarias::select('exercicio_2022.sicap_leis_orcamentarias.*')
                ->join('remessa_parcials as rp', 'rp.id', '=', 'exercicio_2022.sicap_leis_orcamentarias.remessa_parcial_id')
                ->join('remessas as r', 'r.id', '=', 'rp.remessa_id')
                ->join('periodo_remessas as pr', 'pr.id', '=', 'r.periodo_remessa_id')
                ->where('r.unidade_gestora_id', $analise->unidade_gestora_id)
                ->where('pr.exercicio', $analise->exercicio)
                ->first();

            if ($leiOrcamentaria) {
                return preg_replace_callback($varPattern, function ($matches) use ($leiOrcamentaria) {
                    $variavel = $matches[2];
                    $this->variaveis[$variavel] = $leiOrcamentaria->{$variavel};
                    $variavel = strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $variavel));

                    if (str_starts_with($variavel, 'data')) {
                        return Carbon::parse($leiOrcamentaria->{$variavel})->format('d/m/Y');
                    }

                    if (str_starts_with($variavel, 'valor')) {
                        return number_format((float) $leiOrcamentaria->{$variavel}, 2, ',', '.');
                    }

                    return $leiOrcamentaria->{$variavel};
                }, $texto);
            }
        }

        return $texto;
    }

    /**
     * Substitui variáveis de análise no texto fornecido usando dados de uma análise.
     * Utiliza um padrão regex para identificar variáveis com ou sem deslocamento de ano.
     * Exemplos: '$_al_Atual{Exercício}' e '$_al_Atual-1{Exercício}'.
     *
     * @param  string  $texto  O texto que contém as variáveis a serem substituídas.
     * @param  object  $analise  Objeto contendo os dados necessários para a substituição.
     * @return string O texto com as variáveis substituídas.
     */
    public function replaceVariaveisAnalise($texto, $analise)
    {
        $varPattern = '/\$_al(?:_Atual(-\d+)?)?\{(.*?)\}/';

        return preg_replace_callback($varPattern, function ($matches) use ($analise) {
            $variavel = $matches[2];

            if ($variavel === 'Responsáveis Técnicos') {
                $nomes = $analise->unidadeGestora->responsavelTecnico?->pluck('name')->toArray() ?? [];
                if (empty($nomes)) {
                    return 'N/A';
                }
                $ultimoNome = array_pop($nomes);

                return $nomes ? implode(', ', $nomes).' e '.$ultimoNome : $ultimoNome;
            }

            if ($variavel === 'Exercício') {
                $offset = empty($matches[1]) ? 0 : -(int) $matches[1];

                return $this->calcularAno($offset, (int) $analise->exercicio);
            }

            if ($variavel === 'População') {
                $this->variaveis[$variavel] = (float) $analise->unidadeGestora->cidade->populacao;

                return number_format((float) $analise->unidadeGestora->cidade->populacao, 0, '', '.');
            }

            $variableMap = [
                'Modelo Análise' => $analise->modeloAnalise->nome,
                'Município' => $analise->unidadeGestora->cidade->nome,
                'Unidade Gestora' => $analise->unidadeGestora->nome,
                'Data de criação' => $analise->data_criacao->format('d/m/Y'),
                'Data de conclusão' => $analise->data_conclusao?->format('d/m/Y'),
                'Assinatura' => $analise->assinatura,
                'Status' => $analise->status,
                'Diretoria' => $analise->diretoria->nome ?? '',
                'Protocolo eTCE' => $analise->protocolo_etce ?? '',
                'Gestor' => $analise->unidadeGestora->gestor->first()->name ?? null,
                'Controlador' => $analise->unidadeGestora->controlador->first()->name ?? null,
                'População' => number_format((float) $analise->unidadeGestora->cidade->populacao, 0, '', '.'),
            ];

            return $variableMap[$variavel];
        }, $texto);
    }

    public function replaceFormulas($texto, $analise)
    {
        // pegar todas as fórmulas e colocar no evaluate para calcular o seu valor, passando as variáveis calculadas anteriormente
        $listaFormulas = $this->getFormulas($texto);

        $language = new ExpressionLanguage;
        foreach ($listaFormulas as $itemFormula) {
            try {
                $formulaEvaluated = $language->evaluate($itemFormula, $this->variaveis);

                if (is_numeric($formulaEvaluated)) {
                    $texto = str_replace('==#'.$itemFormula.'#', number_format((float) $formulaEvaluated, 2, ',', '.'), $texto);
                } else {
                    $texto = str_replace('==#'.$itemFormula.'#', $formulaEvaluated, $texto);
                }
            } catch (\Symfony\Component\ExpressionLanguage\SyntaxError $e) {
                Log::error('[SAPC] Erro de sintaxe na fórmula.', [
                    'expressao' => $itemFormula,
                    'erro' => $e->getMessage(),
                    // 'posicao' => $e->getToken()->getCursor(), // se disponível
                ]);
            } catch (\TypeError $e) {
                Log::error('[SAPC] Erro de tipo ao avaliar a fórmula.', [
                    'expressao' => $itemFormula,
                    'erro' => $e->getMessage(),
                    'arquivo' => $e->getFile(),
                    'linha' => $e->getLine(),
                ]);
            } catch (\Exception $e) {
                Log::error('[SAPC] Erro inesperado ao avaliar a fórmula.', [
                    'expressao' => $itemFormula,
                    'erro' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        return $texto;
    }
}
