<?php

namespace App\Nova\Sapc;

use App\Enums\Sapc\CertidaoStatusEnum;

use App\Nova\Cidade;
use App\Nova\Filters\CidadeCidade;
use App\Nova\Filters\EnumFilter;
use App\Nova\Resource;
use App\Nova\User;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaDetachedFilters\HasDetachedFilters;
use Outl1ne\NovaDetachedFilters\NovaDetachedFilters;
use SimpleSquid\Nova\Fields\Enum\Enum;
use App\Nova\Filters\Sapc\ModeloAnalise as FiltersModeloAnalise;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\Text;

class CertidaoPVL extends Resource
{
    use HasDetachedFilters;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\CertidaoPVL>
     */
    public static $model = \App\Models\Sapc\CertidaoPVL::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';
    public static $group = 'Certidões';
    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'modelo_analise_id',
        'cidade_id',
        'status'
    ];

    public static function label()
    {
        return 'Certidão PVL';
    }

    public static function singularLabel()
    {
        return 'Certidão PVL';
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->select('sapc.certidao_pvl.*')
            ->leftJoin('sapc.modelo_analise', 'sapc.certidao_pvl.modelo_analise_id', '=', 'sapc.modelo_analise.id')
            ->leftJoin('cidades', 'sapc.certidao_pvl.cidade_id', '=', 'cidades.id')
            ->leftJoin('users', 'sapc.certidao_pvl.user_id', '=', 'users.id');
    }

    protected static function applySearch($query, $search)
    {
        return $query->select('sapc.certidao_pvl.*')
            ->leftJoin('sapc.modelo_analise ma', 'sapc.certidao_pvl.modelo_analise_id', '=', 'sapc.modelo_analise.id')
            ->leftJoin('cidades as c', 'c.id', '=', 'sapc.certidao_pvl.cidade_id')
            ->where('c.nome', 'ilike', "%{$search}%")
            ->OrWhere('u.name', 'ilike', "%{$search}%")
            ->OrWhere('ma.nome', 'ilike', "%{$search}%")
            ->OrWhere('sapc.certidao_pvl.status', 'ilike', "%{$search}%");
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Modelo'), 'modeloAnalise', ModeloAnalise::class)->sortable()->required()->withMeta(['sortableUriKey' => 'modelo_analise_nome']),
            BelongsTo::make(__('Usuário'), 'user', User::class)->sortable()->required(),
            BelongsTo::make(__('Cidade'), 'cidade', Cidade::class)->sortable()->required(),
            Date::make('Data Criação', 'data_criacao')->required()->hideFromIndex(),
            Date::make('Data Finalizada', 'data_finalizada')->required()->hideFromIndex(),
            Enum::make(__('Status'), 'status')->attach(CertidaoStatusEnum::class)->sortable(),
            Text::make(__('Ofício'), 'oficio')->required()->sortable(),
            HasMany::make(__('Análises Certidões'), 'formularios', CertidaoPVLFormulario::class)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new NovaDetachedFilters([
                (new FiltersModeloAnalise)->withMeta(['width' => 'w-1/3']),
                (new CidadeCidade)->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('status', 'App\Enums\Sapc\CertidaoStatusEnum'))->withMeta(['width' => 'w-1/3']),
            ]))->width('full'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
