<?php

namespace App\Nova\Sapc;

use App\Enums\Sapc\StatusAnaliseFormulario;
use App\Nova\Certidao;
use Bradoctech\SapcNovaCkEditor\CkEditor;
use Laravel\Nova\Fields\ID;
use App\Nova\Resource;
use App\Nova\User;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\Number;
use SimpleSquid\Nova\Fields\Enum\Enum;

class CertidaoPVLFormulario extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\CertidaoPVLFormulario>
     */
    public static $model = \App\Models\Sapc\CertidaoPVLFormulario::class;
    public static $title = 'modeloFormulario.nome';
    public static $group = 'Certidões';
    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */


    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'certidao_id',
        'modelo_formulario_id',
        'user_id',
        'data_criacao',
        'data_preenchimento',
        'versao',
        'status'
    ];

    public static $displayInNavigation = false;

    public static function uriKey()
    {
        return 'certidoes-pvl-formularios';
    }

    public static function singularLabel()
    {
        return 'Análise de Certidões PVL';
    }

    protected static function applySearch($query, $search)
    {
        return $query->select('sapc.certidao_pvl_formulario.*')
            ->leftJoin('sapc.certidao_pvl as c', 'c.id', '=', 'sapc.certidao_pvl_formularios.certidao_id')
            ->leftJoin('sapc.modelo_formulario as mf', 'mf.id', '=', 'sapc.certidao_pvl_formulario.modelo_formulario_id')
            ->leftJoin('users as u', 'u.id', '=', 'sapc.certidao_pvl_formulario.user_id')
            ->where('u.name', 'ilike', "%{$search}%")
            ->OrWhere('mf.nome', 'ilike', "%{$search}%")
            ->OrWhere('sapc.certidao_pvl_formulario.data_criacao', 'ilike', "%{$search}%")
            ->OrWhere('sapc.certidao_pvl_formulario.data_preenchimento', 'ilike', "%{$search}%")
            ->OrWhere('sapc.certidao_pvl_formulario.versao', 'ilike', "%{$search}%")
            ->OrWhere('sapc.certidao_pvl_formulario.status', 'ilike', "%{$search}%");
    }
    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Certidão PVL'), 'certidao', CertidaoPVL::class)->required(),
            BelongsTo::make(__('Modelo Formulário'), 'modeloFormulario', ModeloFormulario::class)->required(),
            BelongsTo::make(__('Usuário'), 'user', User::class)->required(),
            Date::make(__('Data Criação'), 'data_criacao')->required(),
            Date::make(__('Data Preenchimento'), 'data_preenchimento')->required(),
            CkEditor::make(trans('Texto'), 'texto')->stacked()->rules(['required']),
            Number::make(__('Versão'), 'versao')->required(),
            Enum::make(__('Status'))->attach(StatusAnaliseFormulario::class)->displayUsingLabels(),
            HasMany::make('Versão de Forumulario de Certidão', 'versoes', CertidaoPVLFormularioVersao::class)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
