<?php

namespace App\Nova\Sapc;

use Advoor\NovaEditorJs\NovaEditorJsField;
use App\Nova\Resource;
use App\Nova\User;
use Bradoctech\SapcNovaCkEditor\CkEditor;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CertidaoPVLFormularioVersao extends Resource
{
    public static $displayInNavigation = false;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\CertidaoPVLFormularioVersao>
     */
    public static $model = \App\Models\Sapc\CertidaoPVLFormularioVersao::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'texto',
        'versao',
        'data_preenchimento',
        'user_id'
    ];

    public static function uriKey()
    {
        return 'analises-formularios-versoes';
    }

    public static function label()
    {
        return 'Versões de formulários de certidões';
    }

    public static function singularLabel()
    {
        return 'Versão de formulário de certidão';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Formulario de Certidão'), 'formulario', CertidaoPVLFormulario::class)->required(),
            BelongsTo::make(__('Usuário'), 'user', User::class)->required(),
            Date::make(__('Date de preenchimento'), 'data_preenchimento')->required(),
            CkEditor::make(__('Texto'), 'texto')->required(),
            Number::make(__('Versão'), 'versao')->exceptOnForms(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    /**
     * Determine if the user can create new resources.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }
}
