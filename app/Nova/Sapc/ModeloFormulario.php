<?php

namespace App\Nova\Sapc;

use App\Enums\Sapc\CategoriaDocumento;
use App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum;
use App\Enums\Sapc\StatusModeloFormulario;
use App\Nova\Filters\EnumFilter;
use App\Nova\Resource;
use Bradoctech\SapcNovaCkEditor\CkEditor;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\File;
use Laravel\Nova\Fields\FormData;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaDetachedFilters\HasDetachedFilters;
use Outl1ne\NovaDetachedFilters\NovaDetachedFilters;
use SimpleSquid\Nova\Fields\Enum\Enum;

class ModeloFormulario extends Resource
{
    use HasDetachedFilters;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\ModeloFormulario>
     */
    public static $model = \App\Models\Sapc\ModeloFormulario::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'nome';

    public static $group = 'e-Contas';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'nome', 'texto', 'versao', 'status',
    ];

    public static function uriKey()
    {
        return 'modelos-formularios';
    }

    public static function label()
    {
        return 'Modelos de formulários';
    }

    public static function singularLabel()
    {
        return 'modelo de formulário';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $categoriaDocumentoOptions = [];
        foreach (CategoriaDocumento::asArray() as $key) {
            $categoriaDocumentoOptions[$key] = CategoriaDocumento::getDescription($key);
        }

        return [
            ID::make()->sortable(),
            Text::make(__('Nome'), 'nome')->rules(['required'])->sortable(),
            Enum::make(__('Tipo de Formulário'), 'tipo_formulario')
                ->attach(CertidaoPVLTipoModeloFormularioEnum::class)
                ->displayUsingLabels()
                ->default(CertidaoPVLTipoModeloFormularioEnum::CONTEUDO)
                ->sortable(),

            Select::make('Categoria', 'categoria_documento')
                ->options($categoriaDocumentoOptions)
                ->sortable()
                ->displayUsing(fn ($modelo) => CategoriaDocumento::getDescription($modelo)),

            CkEditor::make(trans('Texto'), 'texto')->stacked()->hideFromIndex()->rules(function ($request) {
                return $request->input('tipo_formulario') !== 'marcacao' ? ['required'] : [];
            }),

            Number::make(__('Versão'), 'versao')->readonly()->default('1')->hideFromIndex()->hideWhenCreating()->hideWhenUpdating()->sortable(),
            Enum::make(__('Status'))->attach(StatusModeloFormulario::class)->displayUsingLabels()->sortable(),

            Select::make('Orientação', 'orientacao')
                ->options([
                    'portrait' => 'Retrato',
                    'landscape' => 'Paisagem',
                ])
                ->displayUsingLabels()
                ->default('portrait')
                ->rules(function (NovaRequest $request) {
                    return [
                        function ($attribute, $value, $fail) use ($request) {
                            $isLandscape = $value === 'landscape';
                            $isCapa = $request->get('capa') == true;
                            $isContracapa = $request->get('contracapa') == true;
                            $isApendice = $request->get('apendice') == true;

                            if ($isLandscape && ($isCapa || $isContracapa || $isApendice)) {
                                $fail('Formulários do tipo Capa, Contracapa ou Apêndice não podem estar no modo Paisagem.');
                            }
                        },
                        'required',
                        'in:portrait,landscape',
                    ];
                })
                ->sortable(),

            HasMany::make(__('Modelos Análise'), 'modelosAnalisesFormularios', ModeloAnaliseFormulario::class),

            Boolean::make('Capa?', 'capa')
                ->rules(function (NovaRequest $request) {
                    return [
                        function ($attribute, $value, $fail) use ($request) {
                            $isLandscape = $request->get('orientacao') === 'landscape';
                            if ($value && $isLandscape) {
                                $fail('Um formulário marcado como Capa não pode estar no modo Paisagem.');
                            }
                        },
                    ];
                })
                ->sortable()
                ->dependsOn(
                    ['apendice'],
                    $this->exclusiveLock(['apendice']),
                ),

            File::make('Arquivo de capa', 'arquivo_capa')
                ->hideFromIndex()
                ->dependsOn(
                    ['capa'],
                    $this->hideIf('capa'),
                ),

            Boolean::make('ContraCapa?', 'contracapa')
                ->rules(function (NovaRequest $request) {
                    return [
                        function ($attribute, $value, $fail) use ($request) {
                            $isLandscape = $request->get('orientacao') === 'landscape';
                            if ($value && $isLandscape) {
                                $fail('Um formulário marcado como ContraCapa não pode estar no modo Paisagem.');
                            }
                        },
                    ];
                })
                ->sortable()
                ->dependsOn(
                    ['apendice'],
                    $this->exclusiveLock(['apendice']),
                ),

            File::make('Arquivo de Contracapa', 'arquivo_contracapa')
                ->hideFromIndex()
                ->dependsOn(
                    ['contracapa'],
                    $this->hideIf('contracapa'),
                ),

            Boolean::make('Apêndice?', 'apendice')
                ->sortable()
                ->dependsOn(
                    ['capa', 'contracapa'],
                    $this->exclusiveLock(['capa', 'contracapa']),
                ),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new NovaDetachedFilters([
                (new EnumFilter('status', 'App\Enums\Sapc\StatusModeloFormulario'))->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('categoria_documento', 'App\Enums\Sapc\CategoriaDocumento'))->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('tipo_formulario', 'App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum'))->withMeta(['width' => 'w-1/3']),
            ]))->width('full'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }

    /**
     * Oculta o campo de Arquivo quando o Campo Dependente estiver desmarcado
     */
    private function hideIf(string $fieldName): callable
    {
        return function (File $field, NovaRequest $request, FormData $formData) use ($fieldName) {
            if (! $formData->$fieldName) {
                $field->hide();
            }
        };
    }

    /**
     * Desativa o campo quando qualquer campo conflitante estiver ativo
     */
    private function exclusiveLock(array $mutuallyExclusiveFields): callable
    {
        return function (Boolean $field, NovaRequest $request, FormData $formData) use ($mutuallyExclusiveFields) {
            $shouldLock = collect($mutuallyExclusiveFields)->contains(fn ($fieldName) => $formData->$fieldName);
            if ($shouldLock) {
                $field->setValue(false);
            }
            $field->readonly($shouldLock);
        };
    }
}
