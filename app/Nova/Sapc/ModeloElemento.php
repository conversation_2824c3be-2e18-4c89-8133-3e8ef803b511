<?php

namespace App\Nova\Sapc;

use App\Nova\Resource;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ModeloElemento extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\ModeloElemento>
     */
    public static $model = \App\Models\Sapc\ModeloElemento::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    /**
     * Os campos que podem ser usados para pesquisa.
     */
    public static $search = [
        'id', 'variavel',
    ];

    public static $group = 'e-Contas';

    /**
     * Os campos que serão exibidos na tela de index, detalhes, criação e edição.
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Variável', 'variavel')
                ->rules('required'),

            Text::make('Coluna', 'coluna')
                ->rules('required'),

            Textarea::make('Consulta', 'consulta')
                ->rules('required'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
