<?php

namespace App\Nova\Filters\sapc\CertidoesPvl;

use App\Models\Cidade as ModelsCidade;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CidadeFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public $name = 'Cidade';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('cidade_id', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        $items = ModelsCidade::orderBy('nome')->get();
        $options = [];

        foreach ($items as $item) {
            $options[$item->nome] = $item->id;
        }

        return $options;
    }
}
