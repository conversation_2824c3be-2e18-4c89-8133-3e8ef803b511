<?php

namespace App\Nova\Filters\sapc\CertidoesPvl;

use App\Enums\Esfera as EnumsEsfera;
use Laravel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class EsferaFilter extends Filter
{
    public $name = 'Esfera';

    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('esfera', operator: EnumsEsfera::getValue($value));
    }

    public function options(NovaRequest $request)
    {
        return EnumsEsfera::asSelectArray();
    }
}
