<?php

namespace App\Nova\Filters\sapc\CertidoesPvl;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class AnalisadoFilter extends Filter
{
    /**
     * O componente do filtro no Nova (select-filter).
     */
    public $component = 'select-filter';

    /**
     * Nome do filtro exibido na UI (opcional, se quiser personalizar).
     */
    public $name = 'Analisado';

    /**
     * Aplica o filtro na query.
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('analisado', $value);
    }

    /**
     * Retorna as opções disponíveis no select do filtro.
     */
    public function options(NovaRequest $request)
    {
        return [
            'Analisado'      => 1,
            'Não Analisado'  => 0
        ];
    }
}
