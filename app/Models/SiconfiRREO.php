<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SiconfiRREO extends Model
{
    protected $connection = 'pgsql_siconfi';
    protected $table = 'raw.siconfi_rreo'; 
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'exercicio',
        'demonstrativo',
        'periodo',
        'periodicidade',
        'instituicao',
        'cod_ibge',
        'uf',
        'populacao',
        'anexo',
        'esfera',
        'rotulo',
        'coluna',
        'cod_conta',
        'conta',
        'valor',
        'id_recibo'
    ];

    protected $casts = [
        'exercicio' => 'integer',
        'periodo' => 'integer',
        'cod_ibge' => 'integer',
        'populacao' => 'integer',
        'valor' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
