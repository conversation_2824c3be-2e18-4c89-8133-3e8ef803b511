<?php

namespace App\Models;

use App\Enums\PapelUsuario;
use App\Enums\Sexo;
use App\Enums\TipoUsuario;
use Bradoctech\Brandenburg\Traits\HasRoles;
use App\Models\Sapc\CertidaoPVL;
use Brazanation\Documents\Cpf;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lab404\Impersonate\Models\Impersonate;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory, Notifiable;
    use HasRoles;
    use Impersonate;
    use LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'type',
        'role',
        'cpf',
        'status',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        $options = new LogOptions();

        $options->logOnlyDirty();
        $options->logOnly(['name', 'status', 'cpf', 'password', 'role', 'type', 'email']);

        return $options;
    }

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'can_impersonate',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'type' => TipoUsuario::class,
        'role' => PapelUsuario::class,
        'sexo' => Sexo::class,
        'telefones' => 'array',
        'data_nascimento' => 'date:d/m/Y',
    ];

    /**
     * @return void
     */
    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    /**
     * @return string
     */
    public function getEmailAttribute($value)
    {
        return strtolower($value);
    }

    public function diretorias()
    {
        return $this->belongsToMany(Diretoria::class, 'diretoria_user')
            ->withTimestamps();
    }

    public function certidoes()
    {
        return $this->hasMany(CertidaoPVL::class);
    }

    public function getRemessasHabilitadas()
    {

        return Remessa::join('unidade_gestoras', 'unidade_gestoras.id', 'remessas.unidade_gestora_id')
            ->join('unidade_gestora_user', 'unidade_gestoras.id', 'unidade_gestora_user.unidade_gestora_id')
            ->join('permissoes_remessas', 'permissoes_remessas.unidade_gestora_user_id', 'unidade_gestora_user.id')
            ->join('periodo_remessas', function (JoinClause $join) {
                $join->on('remessas.periodo_remessa_id', '=', 'periodo_remessas.id')
                    ->on('periodo_remessas.exercicio', 'permissoes_remessas.ano')
                    ->on('periodo_remessas.bimestre', 'permissoes_remessas.remessa');
            })
            ->where('unidade_gestora_user.user_id', $this->id)
            ->whereNull('remessas.deleted_at')
            ->where('permissoes_remessas.habilitado', true)
            ->with('unidadeGestora', 'periodoRemessa')
            ->pluck('remessas.id')
            ->toArray();
    }

    public function unidadesGestoras()
    {
        return $this->belongsToMany(UnidadeGestora::class, 'unidade_gestora_user')
            ->withPivot('vinculo_id', 'id', 'role')
            ->withTimestamps();
    }

    private function hasRoleConfigured(): bool
    {
        return ! is_null($this->type) && ! is_null($this->role);
    }

    public function isAdmin(): bool
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->type->is(TipoUsuario::Interno) &&
            $this->role->is(PapelUsuario::Administrador);
    }

    public function getRoleDescription(UnidadeGestora $unidade): ?string
    {
        $role = $this->roleFromUnidadeGestora($unidade);

        return optional($role)->description;
    }

    public function getRoleDescriptionByGender(UnidadeGestora $unidade, ?PapelUsuario $role = null): ?string
    {
        if (is_null($role)) {
            $role = $this->roleFromUnidadeGestora($unidade);
            $role = optional($role)->description;
        } else {
            $role = $role->description;
        }

        if (is_null($this->sexo) || $this->sexo->is(Sexo::Masculino)) {
            return $role;
        }

        if (stripos($role, 'Técnico') !== false) {
            $role = str_replace('Técnico', 'Técnica', $role);
        } elseif (substr($role, -1) === 'o') {
            $role = substr($role, 0, -1).'a';
        } else {
            $role .= 'a';
        }

        return $role;
    }

    public function isInterno(): bool
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->type->is(TipoUsuario::Interno);
    }

    public function isJurisdicionado()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->type->is(TipoUsuario::Jurisdicionado);
    }

    public function isActive()
    {
        if (empty($this->status)) {
            return false;
        }

        return true;
    }

    public function canPortalAuditor()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->isAdmin() ||
            $this->isAuditor() ||
            $this->isConselheiro() ||
            $this->isAnalistaTecnico() ||
            $this->isAutoridadeLegitimada() ||
            $this->isDiretor();
    }

    public function inUnidadeGestora(UnidadeGestora $unidadeGestora): bool
    {
        $unidadesGestoras = $this->unidadesGestoras->filter(function ($item) use ($unidadeGestora) {
            return $item->id == $unidadeGestora->id;
        });

        return $unidadesGestoras->count() > 0;
    }

    protected static function booted()
    {
        static::creating(function ($user) {
            /* forca a ser do tipo jurisdicionado se for importado do LDAP */
            if (! empty($user->guid)) {
                $user->type = TipoUsuario::Jurisdicionado;
            }
        });
    }

    public function toArray()
    {
        $data = parent::toArray();

        return $this->rolesToArray($data);
    }

    public function rolesToArray(array $data): array
    {
        $roles = [];
        foreach ($this->roles as $role) {
            array_push($roles, $role->name);
        }

        $data['roles'] = $roles;

        return $data;
    }

    public function getCPF()
    {
        $cpf = Cpf::createFromString($this->cpf);
        if ($cpf) {
            return $cpf->format();
        }

        return $this->cpf;
    }

    /**
     * Pega o papel do usuario na unidade gestora
     *
     * @param UnidadeGestora unidade de consultao
     * @return \App\Enums\PapelUsuario tipo do usuario na unidade
     */
    public function roleFromUnidadeGestora(UnidadeGestora $unidade, ?array $allowedRules = null): ?PapelUsuario
    {

        if (empty($this->unidadesGestoras)) {
            $builder = $this->unidadesGestoras()
                ->where('unidade_gestoras.id', $unidade->id);
            if ($allowedRules && ! empty($allowedRules)) {
                $builder->WherePivotIn('role', $allowedRules);
            }
            $unidade = $builder->first();
        } else {
            $collection = $this->unidadesGestoras;
            if ($allowedRules && ! empty($allowedRules)) {
                $collection = $collection->whereIn('pivot.role', $allowedRules);
            }
            $unidade = $collection->find($unidade->id);
        }

        if (empty($unidade)) {
            return null;
        }

        $role = $unidade->pivot->role;
        if (is_null($role)) {
            return null;
        }

        return PapelUsuario::fromValue($role);
    }

    public function rolesFromUnidadeGestora(UnidadeGestora $unidade): array
    {
        if (empty($this->unidadesGestoras)) {
            $unidades = $this->unidadesGestoras()
                ->where('unidade_gestoras.id', $unidade->id)
                ->get();
        } else {
            $unidades = $this->unidadesGestoras;
        }

        if (empty($unidades)) {
            return [];
        }

        return $unidades->map(function ($item) {
            $role = $item->pivot->role;

            if (is_null($role)) {
                return null;
            }

            return PapelUsuario::fromValue($role);
        })->toArray();
    }

    /**
     * @return Se o usuário pode se personificar em outro.
     */
    public function canImpersonate()
    {
        return $this->can_impersonate;
    }

    public function canAccessPortalMensagem()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Controlador) || $this->role->is(PapelUsuario::Gestor);
    }

    public function respostas()
    {
        return $this->hasMany(Resposta::class);
    }

    public function mensagens()
    {
        return $this->hasMany(Mensagem::class);
    }

    public function questionariosCriados()
    {
        return $this->hasMany(Questionario::class);
    }

    public function questionariosRespondidos()
    {
        return $this->hasMany(Questionario::class);
    }

    public function scopeApenasUsuariosAdministradores($query)
    {
        return $query->whereRole(PapelUsuario::Administrador)->get();
    }

    public function historico()
    {
        return $this->activities();
    }

    public function isDiretor()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Diretor);
    }

    public function isControlador()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Controlador);
    }

    public function isGestor()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Gestor);
    }

    public function isAnalistaTecnico()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::AnalistaTecnico);
    }

    public function isAuditor()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Auditor);
    }

    public function isAutoridadeLegitimada()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::AutoridadeLegitimada);
    }

    public function isConselheiro()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Conselheiro);
    }

    public function isEstagiario()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Estagiario);
    }

    public function isTerceirizado()
    {
        if (! $this->hasRoleConfigured()) {
            return false;
        }

        return $this->role->is(PapelUsuario::Terceirizado);
    }

    public function receivesBroadcastNotificationsOn()
    {
        return 'notificacao-externa';
    }
}
