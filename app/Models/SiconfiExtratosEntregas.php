<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SiconfiExtratosEntregas extends Model
{
    protected $connection = 'pgsql_siconfi';
    protected $table = 'raw.siconfi_extratos_entregas'; 
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'exercicio',
        'data_status',
        'status_relatorio',
        'entregavel',
        'periodo',
        'periodicidade',
        'cod_ibge',
    ];

    protected $casts = [
        'exercicio' => 'integer',
        'periodo' => 'integer',
        'cod_ibge' => 'integer',
        'valor' => 'float',
        'data_status' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
