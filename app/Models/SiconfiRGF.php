<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SiconfiRGF extends Model
{
    protected $connection = 'pgsql_siconfi';
    protected $table = 'raw.siconfi_rgf'; 
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'exercicio',
        'periodo',
        'periodicidade',
        'instituicao',
        'cod_ibge',
        'uf',
        'co_poder',
        'populacao',
        'anexo',
        'esfera',
        'rotulo',
        'coluna',
        'cod_conta',
        'conta',
        'valor',
        'id_recibo'
    ];

    protected $casts = [
        'exercicio' => 'integer',
        'periodo' => 'integer',
        'cod_ibge' => 'integer',
        'populacao' => 'integer',
        'valor' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
