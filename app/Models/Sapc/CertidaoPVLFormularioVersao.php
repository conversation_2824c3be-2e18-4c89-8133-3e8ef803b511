<?php

namespace App\Models\Sapc;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CertidaoPVLFormularioVersao extends Model
{
    use HasFactory;

    protected $table = 'sapc.certidao_pvl_formularios_versao';

    protected $fillable = [
        'certidao_formulario_id',
        'data_preenchimento',
        'texto',
        'versao',
        'user_id',
    ];

    protected $casts = [
        'data_preenchimento' => 'datetime',
    ];

    public function formulario()
    {
        return $this->belongsTo(CertidaoPVLFormulario::class, 'certidao_formulario_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
