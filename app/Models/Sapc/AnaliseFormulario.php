<?php

namespace App\Models\Sapc;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class AnaliseFormulario extends Model
{
    use HasFactory;

    const PRAZO = 120;

    protected $table = 'sapc.analise_formulario';

    protected $fillable = [
        'analise_id',
        'modelo_formulario_id',
        'modelo_formulario_versao_id',
        'analise_id',
        'data_criacao',
        'data_preenchimento',
        'texto',
        'versao',
        'user_id',
        'status',
        'texto_auto_save',
    ];

    protected $casts = [
        'data_criacao' => 'datetime',
        'data_preenchimento' => 'datetime',
    ];

    /**
     * Get the analise.
     */
    public function analise()
    {
        return $this->belongsTo(Analise::class);
    }

    /**
     * Get the modeloFormulario.
     */
    public function modeloFormulario()
    {
        return $this->belongsTo(ModeloFormulario::class);
    }

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the analiseFormularioAnexo.
     */
    public function analisesFormulariosAnexos()
    {
        return $this->hasMany(AnaliseFormularioAnexo::class)->with('usuario');
    }

    public function analisesFormulariosVersao()
    {
        return $this->hasMany(AnaliseFormularioVersao::class)->with('usuario')->orderBy('versao', 'desc');
    }

    protected static function booted()
    {
        static::saved(function ($model) {
            // ATUALIZACAO SE STATUS DA ANALISE
            $forms = AnaliseFormulario::query()
                ->where('analise_id', $model->analise_id)
                ->get();

            $analise = Analise::query()
                ->where('id', $model->analise_id)
                ->first();

            $totalForms = $forms->count();
            $finalizadosTempestivos = $forms->filter(fn ($item) => $item->status === 'finalizado' && Carbon::now()->lt($item->data_criacao->addDays(self::PRAZO)));
            $finalizadosIntempestivos = $forms->filter(fn ($item) => $item->status === 'finalizado' && Carbon::now()->gt($item->data_criacao->addDays(self::PRAZO)));
            $atrasados = $forms->filter(fn ($item) => $item->status !== 'finalizado' && Carbon::now()->gt($item->data_criacao->addDays(self::PRAZO)));
            $iniciados = $forms->filter(fn ($item) => $item->status === 'iniciado' && Carbon::now()->lt($item->data_criacao->addDays(self::PRAZO)));
            $totalFinalizadosTempestivos = count($finalizadosTempestivos);
            $totalFinalizadosIntempestivos = count($finalizadosIntempestivos);
            $totalAtrasados = count($atrasados);
            $totalIniciados = count($iniciados);

            if ($totalForms === $totalFinalizadosTempestivos) {
                $analise->status = 'finalizada_tempestiva';
                $analise->save();
            } elseif ($totalForms === $totalFinalizadosIntempestivos) {
                $analise->status = 'finalizada_intempestiva';
                $analise->save();
            } elseif ($totalAtrasados > 0) {
                $analise->status = 'atrasada';
                $analise->save();
            } elseif ($totalIniciados > 0) {
                $analise->status = 'iniciada';
                $analise->save();
            }
        });
    }
}
