<?php

namespace App\Models\Sapc;

use App\Enums\Esfera;
use App\Models\Cidade;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CidadeExercicioAnalisado extends Model
{
    use HasFactory;

    protected $table = 'sapc.cidade_exercicio_analisado';

    protected $fillable = ['cidade_id', 'exercicio', 'analisado', 'esfera'];

    public $timestamps = true;

    protected $casts = [
        'analisado' => 'boolean',
        'esfera' => Esfera::class
    ];

    public function cidade()
    {
        return $this->belongsTo(Cidade::class, 'cidade_id');
    }
}
