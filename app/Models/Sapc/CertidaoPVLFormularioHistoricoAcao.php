<?php

namespace App\Models\Sapc;

use App\Enums\Sapc\CertidaoFormularioHistoricoAcao;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class CertidaoPVLFormularioHistoricoAcao extends Model
{
    protected $table = 'sapc.certidao_pvl_formulario_historico_acao';
    public $timestamps = false;

    protected $casts = [
        'data_acao' => 'datetime',
    ];

    protected $fillable = [
        'certidao_formulario_id',
        'valor',
        'acao',
        'user_id',
        'data_acao',
    ];

    protected $appends = [
        'acao_texto',
    ];

    public function getAcaoTextoAttribute()
    {
        return CertidaoFormularioHistoricoAcao::getText($this->acao);
    }

    public function formulario()
    {
        return $this->belongsTo(CertidaoPVLFormulario::class, 'certidao_formulario_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
