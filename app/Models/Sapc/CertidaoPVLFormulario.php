<?php

namespace App\Models\Sapc;

use App\Enums\Sapc\CertidaoFormularioStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CertidaoPVLFormulario extends Model
{
    use HasFactory;

    protected $table = 'sapc.certidao_pvl_formularios';

    protected $fillable = [
        'certidao_id',
        'modelo_formulario_id',
        'data_criacao',
        'data_preenchimento',
        'texto',
        'versao',
        'user_id',
        'status',
        'texto_auto_save',
        'exercicio'
    ];

    protected $casts = [
        'data_criacao' => 'datetime',
        'data_preenchimento' => 'datetime',
        'status' => CertidaoFormularioStatus::class
    ];

    public function certidao()
    {
        return $this->belongsTo(CertidaoPVL::class, 'certidao_id');
    }

    public function modeloFormulario()
    {
        return $this->belongsTo(ModeloFormulario::class, 'modelo_formulario_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function versoes()
    {
        return $this->hasMany(CertidaoPVLFormularioVersao::class, 'certidao_formulario_id');
    }
}
