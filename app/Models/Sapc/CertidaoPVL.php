<?php

namespace App\Models\Sapc;

use App\Enums\Esfera;
use App\Enums\Sapc\CertidaoStatusEnum;
use App\Models\Cidade;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CertidaoPVL extends Model
{
    use HasFactory;

    protected $table = 'sapc.certidao_pvl';

    protected $primaryKey = 'id';

    protected $fillable = [
        'modelo_analise_id',
        'cidade_id',
        'data_criacao',
        'data_finalizada',
        'status',
        'esfera',
        'oficio',
        'user_id',
        'declaracao_instituicao',
        'declaracao_limites',
        'declaracao_operacoes',
    ];

    protected $casts = [
        'data_criacao' => 'datetime',
        'data_finalizada' => 'datetime',
        'esfera' => Esfera::class,
        'status' => CertidaoStatusEnum::class,
        'declaracao_instituicao' => 'boolean',
        'declaracao_limites' => 'boolean',
        'declaracao_operacoes' => 'boolean',
    ];

    public function modeloAnalise()
    {
        return $this->belongsTo(ModeloAnalise::class);
    }

    public function cidade()
    {
        return $this->belongsTo(Cidade::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function formularios()
    {
        return $this->hasMany(CertidaoPVLFormulario::class, 'certidao_id');
    }
}
