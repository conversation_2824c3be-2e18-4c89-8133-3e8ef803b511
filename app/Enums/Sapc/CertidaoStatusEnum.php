<?php

namespace App\Enums\Sapc;

use BenSampo\Enum\Enum;

final class CertidaoStatusEnum extends Enum
{
    public const INICIADA = 'iniciada';
    public const EM_ANDAMENTO = 'em_andamento';
    public const EMITIDA  = 'emitida';
    public const VENCIDA  = 'vencida';
    public const CONCLUIDA = 'concluida';

    public static function getDescription($value): string
    {
        if ($value === self::CONCLUIDA) {
            return 'Concluída';
        }

        return parent::getDescription($value);
    }
}
