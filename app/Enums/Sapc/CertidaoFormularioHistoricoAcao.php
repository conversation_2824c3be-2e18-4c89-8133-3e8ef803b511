<?php

namespace App\Enums\Sapc;

use BenSampo\Enum\Enum;

final class CertidaoFormularioHistoricoAcao extends Enum
{
    public const SalvarFormulario = 'salvar_formulario';
    public const FinalizarFormulario = 'finalizar_formulario';
    public const RestaurarVersao = 'restaurar_versao';
    public const ReabrirFormulario = 'reabrir_formulario';

    public static function getText($acao)
    {
        return [
            self::SalvarFormulario => 'Salvar Formulário',
            self::FinalizarFormulario => 'Finalizar Formulário',
            self::RestaurarVersao => 'Restaurar Versão',
            self::ReabrirFormulario => 'Reabrir Formulário',
        ][$acao];
    }
}
