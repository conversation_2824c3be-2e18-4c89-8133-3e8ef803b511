<?php

namespace App\Enums\Sapc;

use BenSampo\Enum\Enum;

final class CategoriaDocumento extends Enum
{
    public const PRESTACAO_CONTAS_ECONTAS = 1;

    public const CERTIDAO = 2;

    public static function getDescription($value): string
    {
        return [
            self::PRESTACAO_CONTAS_ECONTAS => 'Prestação de Contas (e-Contas)',
            self::CERTIDAO => 'Certidão',
        ][$value] ?? 'Descrição não encontrada';
    }
}
