<?php

namespace App\Enums\Sapc;

use BenSampo\Enum\Enum;

final class CertidaoFormularioStatus extends Enum
{
    public const AGUARDANDO_INICIO = 'aguardando_inicio';
    public const INICIADO = 'iniciado';
    public const FINALIZADO = 'finalizado';
    public const REABERTO = 'reaberto';

    public static function getDescription($value): string
    {
        if ($value === self::AGUARDANDO_INICIO) {
            return 'Aguardando início';
        }
        return parent::getDescription($value);
    }
}
