<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class Esfera extends Enum
{
    const Federal = 0;

    const Estadual = 1;

    const Municipal = 2;

    const EstadualEMunicipal = 3;

    public static function asSelectArray(): array
    {
        return [
            self::Estadual => 'Estadual',
            self::Municipal => 'Municipal',
        ];
    }
  
    public static function getDescription($value): string
    {
        switch ($value) {
            case self::Federal:
                return 'Federal';
            case self::Estadual:
                return 'Estadual';
            case self::Municipal:
                return 'Municipal';
            case self::EstadualEMunicipal:
                return 'Estadual e Municipal';
            default:
                return 'Esfera não encontrada';
        }
    }
}
