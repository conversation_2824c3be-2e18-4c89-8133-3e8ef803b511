{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["env.d.ts", "components.d.ts", "resources/ts/src/**/*", "resources/ts/src-auditor/**/*", "resources/ts/src/**/*.vue", "resources/ts/src-auditor/**/*.vue"], "exclude": ["resources/ts/src/**/__tests__/*", "resources/ts/src-auditor/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./resources/ts/src/*"]}, "types": ["unplugin-icons/types/vue"]}}