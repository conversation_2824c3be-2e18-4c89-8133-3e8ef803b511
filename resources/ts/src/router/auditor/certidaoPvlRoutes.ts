import { featureLayoutV2 } from "@/config/FeatureFlagsConfig";

export const certidaoPvlRoutes = [
  {
    path: "/certidao-pvl",
    redirect: "/painel",
    meta: {
      middleware: "auth"
    },
    component: featureLayoutV2
      ? () => import("@/components/templates/TDefault.vue")
      : () => import("@/layouts-metronic-auditor/main-layout/MainLayout.vue"),
    children: [
      {
        name: "certidao-pvl-certidoes",
        path: "/certidao-pvl/certidoes",
        component: () => import("@/components/pages/PCertidaoPvlCertidoes.vue"),
        meta: {
          pageTitle: "Certidão PVL - Certidões",
          breadcrumbs: ["Certidão PVL - Certidões"]
        }
      }
    ]
  },
  {
    path: "/certidao-pvl",
    redirect: "/painel",
    meta: {
      middleware: "auth"
    },
    component: featureLayoutV2
      ? () => import("@/components/templates/TDefault.vue")
      : () => import("@/layouts-metronic-auditor/main-layout/MainLayout.vue"),
    children: [
      {
        name: "certidao-pvl-certidao",
        path: "/certidao-pvl/certidao/:certidaoId",
        component: () => import("@/components/pages/PCertidaoPvlCertidao.vue"),
        meta: {
          pageTitle: "Certidão PVL - Certidão",
          breadcrumbsLink: [
            {
              title: "Certidão PVL - Certidões",
              link: "/certidao-pvl/certidoes"
            },
            {
              title: "Certidão"
            }
          ]
        }
      }
    ]
  },
  {
    path: "/certidao-pvl",
    redirect: "/painel",
    meta: {
      middleware: "auth"
    },
    component: featureLayoutV2
      ? () => import("@/components/templates/TDefault.vue")
      : () => import("@/layouts-metronic-auditor/main-layout/MainLayout.vue"),
    children: [
      {
        name: "certidao-pvl-certidao-formulario",
        path: "/certidao-pvl/certidao/:certidaoId/formulario/:formularioId",
        component: () =>
          import("@/components/pages/PCertidaoPvlCertidaoFormulario.vue"),
        meta: {
          pageTitle: "Certidão PVL - Formulário",
          breadcrumbsLink: [
            {
              title: "Certidão PVL - Certidões",
              link: "/certidao-pvl/certidoes"
            },
            {
              title: "Certidão PVL - Certidão",
              link: "/certidao-pvl/certidao/:certidaoId"
            },
            {
              title: "Formulário"
            }
          ]
        }
      }
    ]
  },
  {
    path: "/certidao-pvl",
    redirect: "/painel",
    meta: {
      middleware: "auth"
    },
    component: featureLayoutV2
      ? () => import("@/components/templates/TDefault.vue")
      : () => import("@/layouts-metronic-auditor/main-layout/MainLayout.vue"),
    children: [
      {
        name: "certidao-pvl-certidao-formulario-versao",
        path: "/certidao-pvl/certidao/:certidaoId/formulario/:formularioId/versao/:versaoId",
        component: () =>
          import("@/components/pages/PCertidaoPvlCertidaoFormularioVersao.vue"),
        meta: {
          pageTitle: "Certidão PVL - Formulário",
          breadcrumbsLink: [
            {
              title: "Certidão PVL - Certidões",
              link: "/certidao-pvl/certidoes"
            },
            {
              title: "Certidão PVL - Certidão",
              link: "/certidao-pvl/certidao/:certidaoId"
            },
            {
              title: "Certidão PVL - Formulário",
              link: "/certidao-pvl/certidao/:certidaoId/formulario/:formularioId"
            },
            {
              title: "Versão do formulário"
            }
          ]
        }
      }
    ]
  }
];
