import {
  createRouter,
  createWebHashHistory,
  type RouteRecordRaw
} from "vue-router";
import { useAuthStore } from "@/stores/auditor/authStore";
import { useConfigStore } from "@/stores/auditor/config";
import { rootRoutes } from "./rootRoutes";
import { sapcRoutes } from "./sapcRoutes";
import { messageRoutes } from "./messageRoutes";
import { certidaoPvlRoutes } from "./certidaoPvlRoutes";
import { errorRoutes } from "./errorRoutes";

const routes: Array<RouteRecordRaw> = [
  ...rootRoutes,
  ...sapcRoutes,
  ...messageRoutes,
  ...certidaoPvlRoutes,
  ...errorRoutes
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const configStore = useConfigStore();

  // current page view title
  document.title = `${to.meta.pageTitle} - ${import.meta.env.VITE_APP_NAME}`;

  // reset config to initial state
  configStore.resetLayoutConfig();

  // verify auth token before each page change
  authStore.verifyAuth();

  next();

  // Scroll page to top on every route change
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: "smooth"
  });
});

export default router;
