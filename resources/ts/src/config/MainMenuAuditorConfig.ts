export interface MenuItem {
  type: string;
  heading?: string;
  sectionTitle?: string;
  route?: string;
  items?: Array<MenuItem>;
  icon?: any;
  sub?: Array<MenuItem>;
  external?: boolean;
  action?: () => void;
}

import IconSolarWidgetBoldDuotone from "~icons/solar/widget-bold-duotone?width=1rem&height=1rem";
import IconSolarClipboardListBoldDuotone from "~icons/solar/clipboard-list-bold-duotone";
import IconSolarCardSearchBoldDuotone from "~icons/solar/card-search-bold-duotone";
import IocnSolarBuildingsBoldDuotone from "~icons/solar/buildings-bold-duotone";
import IconSolarChart2BoldDuotone from "~icons/solar/chart-2-bold-duotone";
import IconSolarDialogBoldDuotone from "~icons/solar/dialog-bold-duotone";
import IconSolarDiplomaBoldDuotone from "~icons/solar/diploma-bold-duotone";

const MainMenuAuditorConfig: Array<MenuItem> = [
  {
    type: "item",
    heading: "Painel",
    route: "/painel",
    icon: IconSolarWidgetBoldDuotone
  },
  {
    type: "heading",
    heading: "Relatórios"
  },
  {
    type: "item",
    heading: "Relatórios ",
    route: `${import.meta.env.VITE_APP_URL_REPORTS}`,
    icon: IconSolarClipboardListBoldDuotone,
    external: true
  },
  {
    type: "heading",
    heading: "Auditoria"
  },
  {
    type: "item",
    heading: "Central de Mensagens",
    route: "/mensagens",
    icon: IconSolarDialogBoldDuotone
  },
  {
    type: "item",
    heading: "Buscar por Inconsistências",
    route: "/inconsistenciascontabeis",
    icon: IconSolarCardSearchBoldDuotone
  },
  {
    type: "item",
    heading: "Buscar por Unidades Gestoras",
    route: "/buscaUnidadeGestora",
    icon: IocnSolarBuildingsBoldDuotone
  },
  {
    type: "item",
    heading: "e-Contas",
    route: "/e-contas/analises",
    icon: IconSolarChart2BoldDuotone
  },
  {
    type: "item",
    heading: "Certidões",
    route: "/certidao-pvl/certidoes",
    icon: IconSolarDiplomaBoldDuotone
  }
];

export default MainMenuAuditorConfig;
