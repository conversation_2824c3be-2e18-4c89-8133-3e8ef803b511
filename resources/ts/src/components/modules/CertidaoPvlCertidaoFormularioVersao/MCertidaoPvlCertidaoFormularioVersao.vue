<template>
  <AiContainer>
    <div class="ai-flex ai-items-center ai-justify-between">
      <AiContainerTitle>
        {{ certidaoPvlFormularioVersao?.formulario.nome }}
      </AiContainerTitle>
    </div>

    <AiContainerContent>
      <div class="ai-grid-rows[repeat(1,auto)] ai-mb-4 ai-grid ai-grid-cols-4">
        <div>
          Visualizando versão:
          <div class="ai-text-base ai-font-semibold">
            #{{ certidaoPvlFormularioVersao?.versao }}
          </div>
        </div>

        <div>
          Autor:
          <div class="ai-text-base ai-font-semibold">
            {{ certidaoPvlFormularioVersao?.usuario?.name }}
          </div>
        </div>

        <div class="ai-pl-[25%]">
          Data de Preenchimento:
          <div class="ai-text-base ai-font-semibold">
            {{
              formatDateTime(certidaoPvlFormularioVersao?.data_preenchimento)
            }}
          </div>
        </div>

        <div class="ai-w-full ai-text-right">
          Versão atual:
          <div class="ai-text-base ai-font-semibold">
            #{{ certidaoPvlFormularioVersao?.formulario.versao }}
          </div>
        </div>
      </div>

      <div
        ref="diffHeaderWrapperRef"
        class="ai-relative ai-rounded-sm ai-bg-white"
      >
        <div ref="diffHeaderRef" class="ai-flex">
          <div
            class="ai-w-1/2 ai-rounded-tl-sm ai-border-r ai-border-gray-100 ai-bg-gray-200 ai-py-1 ai-pl-4 ai-text-lg ai-font-bold"
          >
            Versão {{ certidaoPvlFormularioVersao?.versao }}
          </div>

          <div
            class="ai-w-1/2 ai-rounded-tr-sm ai-bg-gray-200 ai-p-1 ai-pl-4 ai-text-lg ai-font-bold"
          >
            Versão {{ certidaoPvlFormularioVersao?.formulario?.versao }} (atual)
          </div>
        </div>

        <div
          class="diff ai-float-left ai-w-1/2 ai-overflow-x-scroll ai-break-words ai-p-5"
          v-html="certidaoPvlFormularioVersao?.diff_current"
          style="
            width: 50%;
            float: left;
            overflow-x: scroll;
            padding: 20px;
            word-break: break-word;
          "
        ></div>

        <div
          class="diff ai-float-right ai-w-1/2 ai-overflow-x-scroll ai-break-words ai-p-5"
          v-html="certidaoPvlFormularioVersao?.diff_old"
          style="
            width: 50%;
            float: right;
            overflow-x: scroll;
            padding: 20px;
            word-break: break-word;
          "
        ></div>
      </div>
    </AiContainerContent>

    <AiContainerFooter>
      <AiButton variant="outlined" @click="handleBackButtonClick()">
        <AiIcon>
          <IconSolarArrowLeftLinear />
        </AiIcon>
        Voltar
      </AiButton>

      <AiButton @click="restoreVersion()">
        <AiIcon>
          <IconSolarRestartLinear />
        </AiIcon>
        Restaurar versão #{{ certidaoPvlFormularioVersao?.versao }}
      </AiButton>
    </AiContainerFooter>
  </AiContainer>
</template>

<script lang="ts" setup>
  import type { CertidaoPvlFormularioVersao } from "@/types/CertidaoPvlFormularioVersao";

  import { computed, ref } from "vue";
  import { useRoute, useRouter } from "vue-router";

  import { useCertidaoPvlCertidaoStore } from "@/stores/auditor/certidaoPvlCertidaoStore";

  import { useBackButton } from "@/composables/global/useBackButton";
  import { useFetchCertidaoPvlFormularioRestore } from "@/composables/certidaoPvl/useFetchCertidaoPvlFormularioRestore";
  import { useFetchCertidaoPvlFormularioVersao } from "@/composables/certidaoPvl/useFetchCertidaoPvlFormularioVersao";

  import Toaster from "@/components/Toaster.vue";

  const route = useRoute();
  const router = useRouter();
  const certidaoPvlCertidaoStore = useCertidaoPvlCertidaoStore();

  const certidaoId = computed(() => Number(route.params.certidaoId));
  const formularioId = computed(() => Number(route.params.formularioId));
  const versaoId = computed(() => Number(route.params.versaoId));

  const certidaoPvlFormularioVersao = ref<CertidaoPvlFormularioVersao>();

  const fetchCertidaoPvlFormularioVersao = async () => {
    const { data, error } = await useFetchCertidaoPvlFormularioVersao(
      formularioId.value,
      versaoId.value
    );

    data.value && (certidaoPvlFormularioVersao.value = data.value);

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data.message,
        status: "error"
      });

      await router.replace(
        `/certidao-pvl/certidao/${certidaoId.value}/formulario/${formularioId.value}`
      );
    }
  };

  const restoreVersion = async () => {
    if (!certidaoPvlFormularioVersao.value) return;

    const confirmRestore = window.confirm(
      `Deseja restaurar a versão: #${certidaoPvlFormularioVersao.value.versao}?`
    );

    if (!confirmRestore) return;

    const { data, error } = await useFetchCertidaoPvlFormularioRestore(
      certidaoId.value,
      formularioId.value,
      versaoId.value
    );

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data.message,
        status: "error"
      });
      return;
    }

    Toaster.toast({
      message:
        data.value?.data?.message ||
        `Versão: #${certidaoPvlFormularioVersao.value.versao} restaurada com sucesso!`,
      status: "success"
    });

    await router.replace(
      `/certidao-pvl/certidao/${certidaoId.value}/formulario/${formularioId.value}`
    );
  };

  const formatDateTime = (value: any): string =>
    new Date(value).toLocaleString("pt-BR");

  const { handleBackButtonClick } = useBackButton();

  if (
    isNaN(certidaoId.value) ||
    isNaN(formularioId.value) ||
    isNaN(versaoId.value)
  ) {
    await router.replace(`/certidao-pvl/certidoes`);
  } else {
    await Promise.all([
      fetchCertidaoPvlFormularioVersao(),
      certidaoPvlCertidaoStore.fetch(certidaoId.value)
    ]);
  }
</script>

<style lang="scss">
  /* Difference Highlighting and Strike-through
------------------------------------------------ */
  ins {
    color: #333333;
    background-color: #eaffea;
    text-decoration: none;
  }

  del {
    color: #aa3333;
    background-color: #ffeaea;
    text-decoration: line-through;
  }

  /* Image Diffing
------------------------------------------------ */
  del.diffimg.diffsrc {
    display: inline-block;
    position: relative;
  }

  del.diffimg.diffsrc:before {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        to left top,
        rgba(255, 0, 0, 0),
        rgba(255, 0, 0, 0) 49.5%,
        rgba(255, 0, 0, 1) 49.5%,
        rgba(255, 0, 0, 1) 50.5%
      ),
      repeating-linear-gradient(
        to left bottom,
        rgba(255, 0, 0, 0),
        rgba(255, 0, 0, 0) 49.5%,
        rgba(255, 0, 0, 1) 49.5%,
        rgba(255, 0, 0, 1) 50.5%
      );
  }

  /* List Diffing
------------------------------------------------ */
  /* List Styles */
  .diff {
    list-style: none;
    counter-reset: section;
    display: table;
  }

  .diff > li.normal,
  .diff > li.removed,
  .diff > li.replacement {
    display: table-row;
  }

  .diff > li > div {
    display: inline;
  }

  .diff > li.replacement:before,
  .diff > li.new:before {
    color: #333333;
    background-color: #eaffea;
    text-decoration: none;
  }

  .diff > li.removed:before {
    counter-increment: section;
    color: #aa3333;
    background-color: #ffeaea;
    text-decoration: line-through;
  }

  /* List Counters / Numbering */
  .diff > li.normal:before,
  .diff > li.removed:before,
  .diff > li.replacement:before {
    width: 15px;
    overflow: hidden;
    content: counters(section, ".") ". ";
    display: table-cell;
    text-indent: -1em;
    padding-left: 1em;
  }

  .diff > li.normal:before,
  li.replacement + li.replacement:before,
  .diff > li.replacement:first-child:before {
    counter-increment: section;
  }

  ol.diff li.removed + li.replacement {
    counter-increment: none;
  }

  ol.diff li.removed + li.removed + li.replacement {
    counter-increment: section -1;
  }

  ol.diff li.removed + li.removed + li.removed + li.replacement {
    counter-increment: section -2;
  }

  ol.diff li.removed + li.removed + li.removed + li.removed + li.replacement {
    counter-increment: section -3;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -4;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -5;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -6;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -7;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -8;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -9;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -10;
  }

  ol.diff
    li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.removed
    + li.replacement {
    counter-increment: section -11;
  }

  /* Exception Lists */
  ul.exception,
  ul.exception li:before {
    list-style: none;
    content: none;
  }

  .diff ul.exception ol {
    list-style: none;
    counter-reset: exception-section;
    /* Creates a new instance of the section counter with each ol element */
  }

  .diff ul.exception ol > li:before {
    counter-increment: exception-section;
    content: counters(exception-section, ".") ".";
  }
</style>
