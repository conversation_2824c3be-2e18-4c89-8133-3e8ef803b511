<template>
  <AiStackLayout>
    <AiContainer v-if="!analise">
      <AiContainerHeader>
        <AiContainerTitle> Detalhes da Análise </AiContainerTitle>
      </AiContainerHeader>

      <SkeletonTable />
    </AiContainer>

    <AiContainer v-else>
      <AiContainerHeader>
        <AiContainerTitle>Detalhes da Análise</AiContainerTitle>
      </AiContainerHeader>

      <AiContainerContent>
        <AiTable bordered>
          <AiTableRow>
            <AiTableCell colspan="2">
              <AiTableCellItem>
                <AiTableCellItemLeft>
                  <AiIcon>
                    <IconSolarFileTextOutline />
                  </AiIcon>
                </AiTableCellItemLeft>

                <AiTableCellItemContent>
                  <AiListItemOverline>Nome</AiListItemOverline>

                  <AiListItemText>
                    {{ analise.modelo.nome }}
                  </AiListItemText>
                </AiTableCellItemContent>
              </AiTableCellItem>
            </AiTableCell>
          </AiTableRow>

          <AiTableRow>
            <AiTableCell>
              <AiTableCellItem>
                <AiTableCellItemLeft>
                  <AiIcon>
                    <IconSolarKeyMinimalisticSquare3Outline />
                  </AiIcon>
                </AiTableCellItemLeft>

                <AiTableCellItemContent>
                  <AiListItemOverline>Número</AiListItemOverline>

                  <AiListItemText>
                    {{ analise?.id }}
                  </AiListItemText>
                </AiTableCellItemContent>
              </AiTableCellItem>
            </AiTableCell>

            <AiTableCell>
              <AiTableCellItem>
                <AiTableCellItemLeft>
                  <AiIcon>
                    <IconSolarCalendarMinimalisticOutline />
                  </AiIcon>
                </AiTableCellItemLeft>

                <AiTableCellItemContent>
                  <AiListItemOverline>Exercício</AiListItemOverline>

                  <AiListItemText>
                    {{ analise?.exercicio }}
                  </AiListItemText>
                </AiTableCellItemContent>
              </AiTableCellItem>
            </AiTableCell>
          </AiTableRow>

          <AiTableRow>
            <AiTableCell colspan="2">
              <AiTableCellItem>
                <AiTableCellItemLeft>
                  <AiIcon>
                    <IconSolarShieldOutline />
                  </AiIcon>
                </AiTableCellItemLeft>

                <AiTableCellItemContent>
                  <AiListItemOverline>Unidade Gestora</AiListItemOverline>

                  <AiListItemText>
                    {{ analise?.unidade_gestora.nome_com_cidade }}
                  </AiListItemText>
                </AiTableCellItemContent>
              </AiTableCellItem>
            </AiTableCell>
          </AiTableRow>

          <AiTableRow>
            <AiTableCell colspan="2">
              <AiTableCellItem>
                <AiTableCellItemLeft>
                  <AiIcon>
                    <IconSolarShieldUserOutline />
                  </AiIcon>
                </AiTableCellItemLeft>

                <AiTableCellItemContent>
                  <AiListItemOverline>Diretoria</AiListItemOverline>

                  <AiListItemText>
                    {{ analise?.diretoria.nome }}
                  </AiListItemText>
                </AiTableCellItemContent>
              </AiTableCellItem>
            </AiTableCell>
          </AiTableRow>

          <AiTableRow v-if="analise?.responsavel?.name">
            <AiTableCell colspan="2">
              <AiTableCellItem>
                <AiTableCellItemLeft>
                  <AiIcon>
                    <IconSolarUserIdOutline />
                  </AiIcon>
                </AiTableCellItemLeft>

                <AiTableCellItemContent>
                  <AiListItemOverline>Diretor Responsável</AiListItemOverline>

                  <AiListItemText>
                    {{ analise.responsavel.name }}
                  </AiListItemText>
                </AiTableCellItemContent>
              </AiTableCellItem>
            </AiTableCell>
          </AiTableRow>
        </AiTable>

        <AiInput
          v-model="analise.protocolo_etce"
          label="Protocolo eTCE"
          :maxlength="16"
          :error="protocoloETCEError"
          caption="O protocolo eTCE é salvo automaticamente"
          @update:model-value="salvarProtocoloETCE"
        />
      </AiContainerContent>

      <AiContainerFooter>
        <AiButton
          @click="start"
          v-if="analise.status === 'aguardando_inicio'"
          :disabled="
            loading ||
            loadingForms ||
            !hasRemessas ||
            hasSomeRemessaInadimplente
          "
        >
          <AiIcon>
            <AiSpinner v-if="loading"></AiSpinner>
            <IconSolarPlayOutline v-else />
          </AiIcon>
          Iniciar
        </AiButton>

        <AiTooltipProvider>
          <AiTooltip>
            <AiTooltipTrigger>
              <AiButton
                @click="printAnalise(analise.id)"
                :disabled="isPrintButtonDisabled"
              >
                <AiIcon>
                  <AiSpinner v-if="loading"></AiSpinner>
                  <IconSolarPrinterOutline v-else />
                </AiIcon>
                Imprimir
              </AiButton>
            </AiTooltipTrigger>

            <AiTooltipContent v-if="isPrintButtonDisabled">
              Preencha o protocolo eTCE para poder imprimir
            </AiTooltipContent>
          </AiTooltip>
        </AiTooltipProvider>
      </AiContainerFooter>
    </AiContainer>

    <AiContainer v-if="!hasRemessas">
      <AiContainerHeader>
        <AiContainerTitle> Remessas do Exercício </AiContainerTitle>
      </AiContainerHeader>

      <SkeletonTable />
    </AiContainer>

    <AiContainer v-else>
      <AiContainerHeader>
        <AiContainerTitle>Remessas do Exercício</AiContainerTitle>
      </AiContainerHeader>

      <AiContainerContent>
        <AiTable bordered>
          <AiTableHeader>
            <AiTableRow>
              <AiTableHead>Remessa</AiTableHead>
              <AiTableHead>Período</AiTableHead>
              <AiTableHead>Situação</AiTableHead>
            </AiTableRow>
          </AiTableHeader>
          <AiTableBody>
            <AiTableRow
              v-for="remessa in remessas"
              :key="remessa.id"
              @click="handleTableRemessasRowClick(remessa)"
            >
              <AiTableCell> {{ remessa.nome }}</AiTableCell>

              <AiTableCell>{{ remessa.periodo_descricao }}</AiTableCell>

              <AiTableCell>
                <div class="ai-flex ai-items-center ai-gap-1">
                  <AiIcon
                    v-if="
                      remessa.status === 'waiting-file' ||
                      remessa.status === 'user-canceled'
                    "
                    class="ai-text-danger"
                  >
                    <IconSolarCloseCircleBold />
                  </AiIcon>

                  <AiIcon
                    v-if="remessa.status === 'signed'"
                    class="ai-text-success"
                  >
                    <IconSolarCheckCircleBold />
                  </AiIcon>

                  {{ remessa.situacao }}
                </div>
              </AiTableCell>
            </AiTableRow>
          </AiTableBody>
        </AiTable>
      </AiContainerContent>
    </AiContainer>
  </AiStackLayout>
</template>

<script setup lang="ts">
  import { ref, computed } from "vue";
  import Toaster from "@/components/Toaster.vue";
  import SAPCApi from "@/api/auditor/sapc.js";
  import { useAnaliseStore } from "@/stores/auditor/analiseStore";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useDownloadFile } from "@/composables/global/useDownloadFile";
  import { useModalDownloadStore } from "@/stores/global/modalDownloadStore";
  import { useRoute, useRouter } from "vue-router";

  const route = useRoute();
  const router = useRouter();
  const analiseStore = useAnaliseStore();
  const fetchStore = useFetchStore();
  const modalDownloadStore = useModalDownloadStore();

  const loading = ref(true);
  const loadingForms = ref(false);
  const loadingTable = ref(false);
  const firstFormId = ref<string | null>(null);
  const timer = ref<number>();

  const analiseId = computed(() => Number(route.params?.analiseId) || null);
  const isDownloading = computed(() => modalDownloadStore.isDownloading);
  const analise = computed(() => analiseStore.analise);
  const remessas = computed(() => analiseStore.remessas);
  const hasSomeRemessaInadimplente = computed(
    () => analiseStore.hasSomeRemessaInadimplente
  );
  const hasRemessas = computed(() => analiseStore.hasRemessas);
  const isProtocoloETCEValid = computed(() => {
    return (
      !!analise.value?.protocolo_etce?.trim() &&
      analise.value?.protocolo_etce?.length <= 16
    );
  });
  const protocoloETCEError = computed(() => {
    return isProtocoloETCEValid.value
      ? ""
      : "Valor obrigatório para habilitar o botão imprimir";
  });
  const isPrintButtonDisabled = computed(() => {
    return (
      loading.value ||
      loadingForms.value ||
      isDownloading.value ||
      !hasRemessas.value ||
      !isProtocoloETCEValid.value
    );
  });

  const handleTableRemessasRowClick = (remessa: any) => {
    window.open(
      `${location.origin}/admin/resources/remessa-parcials/${remessa.id}`,
      "_blank"
    );
  };

  const salvarProtocoloETCE = () => {
    clearTimeout(timer.value);

    timer.value = setTimeout(async () => {
      if (!analise.value?.id) {
        Toaster.toast({
          message: "ID da Análise não informado",
          status: "warn"
        });
        return;
      }

      if (!isProtocoloETCEValid.value) return;

      try {
        await analiseStore.updateProtocoloETCE(
          analise.value.id,
          analise.value.protocolo_etce
        );
      } catch (error) {
        handleError(error);
      }
    }, 2000);
  };

  const fetchData = async (analiseId: number) => {
    fetchStore.setFetchState("fetching");
    loading.value = true;
    loadingTable.value = true;

    try {
      await analiseStore.fetchAnalise(analiseId);
      await analiseStore.fetchRemessas(analiseId);
      fetchStore.setFetchState("done");
    } catch (error) {
      fetchStore.setFetchState("error");
      handleError(error);
      console.error(error);
    } finally {
      loading.value = false;
      loadingTable.value = false;
    }
  };

  const backToAnalises = async () => {
    await router.replace({
      name: "analises"
    });
  };

  const fetchForms = async (analiseId: number) => {
    loadingForms.value = true;
    try {
      const formularios = await SAPCApi.fetchFormularios(analiseId);
      loadingForms.value = false;
      firstFormId.value = formularios.data.data[0].id;
      return true;
    } catch (error) {
      loadingForms.value = false;
      console.error(error);
      return false;
    }
  };

  const start = async () => {
    if (
      !analise.value ||
      !hasRemessas.value ||
      hasSomeRemessaInadimplente.value
    )
      return;

    await fetchForms(analiseId.value!);

    if (firstFormId.value) {
      window.location.href = `/e-contas/analise/${analise.value.id}/formulario/${firstFormId.value}`;
    }
  };

  const printAnalise = (id: number) => {
    if (!hasRemessas.value || isDownloading.value) return;

    const municipio =
      analiseStore.analise?.unidade_gestora?.cidade || "município desconhecido";

    useDownloadFile({
      url: `${location.origin}/api/sapc/analise/${id}/download`,
      fileName: `Prestação de Contas de Governo – Prefeitura Municipal de ${municipio}.pdf`
    });
  };

  const handleError = (error: any) => {
    if (error) {
      const status = error?.status ?? error.data.status;
      const message = error?.message ?? error.data.message;

      Toaster.toast({
        message,
        status: status ?? "warn"
      });
    }
  };

  if (!analiseId.value) {
    backToAnalises();
  } else {
    fetchData(analiseId.value);
  }
</script>
