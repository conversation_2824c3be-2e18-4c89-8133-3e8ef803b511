<template>
  <AiStackLayout>
    <AiContainer v-if="isRemessaInadimplenteAlertVisible">
      <AiContainerHeader>
        <div class="ai-flex ai-flex-row ai-gap-2">
          <AiIcon>
            <IconSolarDangerTriangleOutline />
          </AiIcon>
          <AiContainerTitle>Remessas Inadimplentes</AiContainerTitle>
        </div>
      </AiContainerHeader>

      <AiContainerContent>
        Análise não pode ser realizada, existe(m) Remessas Inadimplentes.
      </AiContainerContent>
    </AiContainer>

    <template v-else>
      <CAnaliseFormularioEditor
        :analiseFormulario="analiseFormulario"
        :isFinished="isFinished"
        :processing="processing"
        :editor="editor"
        :autosaveUrl="autosaveUrl"
        :autosaveData="autosaveData"
        @save="save"
        @saveAndFinish="saveAndFinish"
        @reOpenForm="reOpenForm"
        @update:data="handleEditorUpdateData"
        @status-change="handleEditorStatusChange"
        @ready="handleEditorReady"
      />

      <CAnaliseFormularioAnexos
        :anexos="anexos"
        :dataTableAnexo="dataTableAnexo"
        :loadingAnexos="loadingAnexos"
        :isFinished="isFinished"
        :processing="processing"
        @openModal="modalAttachmentIsVisible = true"
        @update:options="updateAnexos"
      />

      <CAnaliseFormularioVersoes
        :listaVersoes="listaVersoes"
        :dataTableVersao="dataTableVersao"
        :loadingVersoes="loadingVersoes"
        :formatDateTime="formatDateTime"
        @verFormularioVersao="verFormularioVersao"
        @update:options="updateVersoes"
      />

      <CAnaliseFormularioHistorico
        :historico="historico"
        :dataTableHistorico="dataTableHistorico"
        :loadingHistorico="loadingHistorico"
        :formatDateTime="formatDateTime"
        @update:options="updateHistorico"
      />
    </template>

    <CModalAttachment
      v-model="modalAttachmentIsVisible"
      title="Anexar Documentos"
      required-file-description
      @close="handleModalAttachmentClose"
    />
  </AiStackLayout>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from "vue";
  import { useRouter, useRoute } from "vue-router";
  import SAPCApi from "@/api/auditor/sapc.js";
  import Toaster from "@/components/Toaster.vue";
  import { useAnaliseStore } from "@/stores/auditor/analiseStore";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useMenuSapcStore } from "@/stores/auditor/menuSapcStore";
  import type {
    AnaliseFormulario,
    Anexo,
    FormularioVersao,
    HistoricoItem,
    DataTableState,
    EditorState,
    EditorStatus
  } from "./types";

  const route = useRoute();
  const router = useRouter();
  if (!route.params.analiseId || !route.params.formularioId) {
    router.replace({ name: "analises" });
  }

  const analiseStore = useAnaliseStore();
  const menuSapcStore = useMenuSapcStore();
  const fetchStore = useFetchStore();

  const isLoadingPage = ref(false);
  const isAnexosFetched = ref(false);
  const loading = ref(true);
  const loadingAnexos = ref(false);
  const loadingVersoes = ref(false);
  const loadingHistorico = ref(false);
  const isRemessaInadimplenteAlertVisible = ref(false);
  const processing = ref(false);
  const texto = ref("");
  const modalAttachmentIsVisible = ref(false);
  const analiseFormulario = ref<AnaliseFormulario>({
    id: "",
    nome: "",
    analise_id: ""
  });
  const anexos = ref<Anexo[]>([]);
  const dataTableAnexo = reactive<DataTableState>({
    totalItems: 0,
    pagination: { page: 1, itemsPerPage: 10 },
    pageCount: 1
  });
  const listaVersoes = ref<FormularioVersao[]>([]);
  const dataTableVersao = reactive<DataTableState>({
    totalItems: 0,
    pagination: { page: 1, itemsPerPage: 10 },
    pageCount: 1
  });
  const historico = ref<HistoricoItem[]>([]);
  const dataTableHistorico = reactive<DataTableState>({
    totalItems: 0,
    pagination: { page: 1, itemsPerPage: 10 },
    pageCount: 1
  });
  const editor = reactive<EditorState>({
    status: { isBusy: false, isDirty: false },
    isSaving: false
  });

  const analiseId = computed(() => Number(route.params?.analiseId) || 0);
  const formularioId = computed(() => Number(route.params?.formularioId) || 0);
  const autosaveUrl = computed(
    (): string =>
      `/sapc/analise/${analiseId.value}/formulario/${formularioId.value}/auto-save`
  );
  const autosaveData = computed(
    (): string => analiseFormulario.value.texto_auto_save || ""
  );
  const isFinished = computed(
    (): boolean => analiseFormulario.value.status === "finalizado"
  );
  const hasSomeRemessaInadimplente = computed(
    (): boolean => analiseStore.hasSomeRemessaInadimplente
  );
  const hasRemessas = computed((): boolean => analiseStore.hasRemessas);
  const hasAnalise = computed((): boolean => !!analiseStore.analise);

  const updateAnexos = (pagination: any) => {
    dataTableAnexo.pagination = pagination;
    if (!isAnexosFetched.value) return;
    fetchAnexos();
  };

  const updateVersoes = (pagination: any) => {
    dataTableVersao.pagination = pagination;
    fetchVersoes();
  };

  const updateHistorico = (pagination: any) => {
    dataTableHistorico.pagination = pagination;
    fetchHistorico();
  };

  const fetchRemessas = async () => {
    try {
      if (!hasRemessas.value) await analiseStore.fetchRemessas(analiseId.value);
    } catch (error) {
      console.error(error);
      handleError(error);
    }
  };

  const fetchAnalise = async () => {
    try {
      if (!hasAnalise.value) await analiseStore.fetchAnalise(analiseId.value);
    } catch (error) {
      console.error(error);
      handleError(error);
    }
  };

  const showRemessaInadimplenteAlert = () => {
    isRemessaInadimplenteAlertVisible.value = true;
  };

  const backToAnalises = () => {
    router.push("/e-contas/analises");
  };

  const loadPage = async () => {
    if (!analiseId.value) {
      backToAnalises();
      return;
    }
    fetchStore.setFetchState("fetching");
    isLoadingPage.value = true;
    try {
      await fetchData();
      await fetchAnexos();
      await fetchVersoes();
      await fetchHistorico();
      fetchStore.setFetchState("done");
      isLoadingPage.value = false;
    } catch (error) {
      fetchStore.setFetchState("error");
      isLoadingPage.value = false;
      console.error(error);
    }
  };

  const verFormularioVersao = (formularioVersaoId: number) => {
    router.push(
      `/e-contas/analise/${analiseId.value}/formulario/${formularioId.value}/versao/${formularioVersaoId}`
    );
  };

  const fetchData = async () => {
    loading.value = true;
    try {
      const objAnalise = await SAPCApi.fetchAnaliseFormulario(
        analiseId.value,
        formularioId.value
      );
      loading.value = false;
      analiseFormulario.value = objAnalise.data.data;
      texto.value = analiseFormulario.value.texto || "";
    } catch (error) {
      loading.value = false;
      console.error(error);
    }
  };

  const fetchAnexos = async () => {
    if (!analiseId.value || !formularioId.value) return;

    loadingAnexos.value = true;
    processing.value = true;
    const filterAnexo = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };
    try {
      const success = await SAPCApi.fetchAnexos(
        dataTableAnexo.pagination,
        filterAnexo
      );
      isAnexosFetched.value = true;
      anexos.value = success.data.data;
      loadingAnexos.value = false;
      processing.value = false;
      const meta = success.data;
      if (!meta) return;
      dataTableAnexo.pagination.page = meta.current_page;
      dataTableAnexo.pagination.itemsPerPage = parseInt(meta.per_page);
      dataTableAnexo.totalItems = meta.total;
      dataTableAnexo.pageCount = meta.last_page;
    } catch (error) {
      loadingAnexos.value = false;
      processing.value = false;
      handleError(error);
      console.error(error);
    }
  };

  const fetchVersoes = async () => {
    if (!analiseId.value || !formularioId.value) return;

    loadingVersoes.value = true;
    const filterVersao = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };
    try {
      const success = await SAPCApi.fetchVersoes(
        dataTableVersao.pagination,
        filterVersao
      );
      listaVersoes.value = success.data.data;
      loadingVersoes.value = false;
      const meta = success.data;
      if (!meta) return;
      dataTableVersao.pagination.page = meta.current_page;
      dataTableVersao.pagination.itemsPerPage = parseInt(meta.per_page);
      dataTableVersao.totalItems = meta.total;
      dataTableVersao.pageCount = meta.last_page;
    } catch (error) {
      loadingVersoes.value = false;
      handleError(error);
      console.error(error);
    }
  };

  const fetchHistorico = async () => {
    if (!analiseId.value || !formularioId.value) return;

    loadingHistorico.value = true;
    const filterHistorico = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };
    try {
      const success = await SAPCApi.fetchHistorico(
        dataTableHistorico.pagination,
        filterHistorico
      );
      historico.value = success.data.data;
      const meta = success.data;
      if (!meta) return;
      dataTableHistorico.pagination.page = meta.current_page;
      dataTableHistorico.pagination.itemsPerPage = parseInt(meta.per_page);
      dataTableHistorico.totalItems = meta.total;
      loadingHistorico.value = false;
      dataTableHistorico.pageCount = meta.last_page;
    } catch (error) {
      loadingHistorico.value = false;
      handleError(error);
      console.error(error);
    }
  };

  const save = () => {
    if (!formularioId.value) return;

    const analiseFormularioParam = {
      formularioId: formularioId.value,
      texto: texto.value,
      status: "iniciado"
    };
    processing.value = true;
    editor.isSaving = true;
    SAPCApi.saveAnaliseFormulario(analiseFormularioParam)
      .then(() => {
        Toaster.toast({
          message: "Formulário salvo com sucesso!",
          status: "success"
        });
        menuSapcStore.fetchMenu(analiseId.value as number); // Usado com o template TDefault
        loadPage();
        loading.value = false;
        processing.value = false;
        editor.isSaving = false;
      })
      .catch((error: any) => {
        Toaster.toast({
          message: error.data.message
            ? error.data.message
            : error.response.data.message,
          status: "warn"
        });
        processing.value = false;
        editor.isSaving = false;
      });
  };

  const saveAndFinish = () => {
    if (!formularioId.value) return;

    const analiseFormularioParam = {
      formularioId: formularioId.value,
      texto: texto.value,
      status: "finalizado"
    };
    processing.value = true;
    editor.isSaving = true;
    SAPCApi.saveAnaliseFormulario(analiseFormularioParam)
      .then(() => {
        Toaster.toast({
          message: "Formulário salvo com sucesso!",
          status: "success"
        });
        menuSapcStore.fetchMenu(analiseId.value as number); // Usado com o template TDefault
        loadPage();
        loading.value = false;
        processing.value = false;
        editor.isSaving = false;
      })
      .catch((error: any) => {
        Toaster.toast({
          message: error.data.message
            ? error.data.message
            : error.response.data.message,
          status: "warn"
        });
        processing.value = false;
        editor.isSaving = false;
      });
  };

  const reOpenForm = () => {
    if (!formularioId.value) return;

    const analiseFormularioParam = {
      analiseId: analiseId.value,
      formularioId: formularioId.value
    };
    processing.value = true;
    SAPCApi.reabrirAnaliseFormulario(analiseFormularioParam)
      .then(() => {
        Toaster.toast({
          message: "Formulário reaberto com sucesso!",
          status: "success"
        });
        menuSapcStore.fetchMenu(analiseId.value as number); // Usado com o template TDefault
        loadPage();
        loading.value = false;
        processing.value = false;
      })
      .catch((error: any) => {
        Toaster.toast({
          message: error.data.message ?? error.response.data.message,
          status: "warn"
        });
        processing.value = false;
      });
  };

  const handleError = (err: any) => {
    const error = err.message ?? err;
    if (error) {
      Toaster.toast({ message: err.message, status: "error" });
    }
  };

  const formatDateTime = (value: string): string => {
    const d = new Date(value);
    return d.toLocaleString("pt-BR");
  };

  const handleEditorStatusChange = (status: EditorStatus) => {
    editor.status = status;
  };

  const handleEditorUpdateData = (data: string) => {
    texto.value = data;
  };

  const handleEditorReady = () => {
    if (hasRemessas.value && hasSomeRemessaInadimplente.value) {
      showRemessaInadimplenteAlert();
    } else {
      loadPage();
    }
  };

  const handleModalAttachmentClose = async ({
    file,
    fileDescription
  }: {
    file: File;
    fileDescription: string;
  }) => {
    if (!file || !fileDescription) return;

    try {
      await SAPCApi.uploadAnexo(
        analiseFormulario.value.analise_id,
        analiseFormulario.value.id,
        file,
        fileDescription
      );
    } catch (error) {
      console.error(error);
    } finally {
      modalAttachmentIsVisible.value = false;
      fetchAnexos();
      fetchVersoes();
      fetchHistorico();
    }
  };

  await fetchAnalise();
  await fetchRemessas();

  if (!hasRemessas.value) router.replace({ name: "analises" });

  watch(
    () => route.params.formularioId,
    async () => {
      await nextTick();
      if (hasRemessas.value && !hasSomeRemessaInadimplente.value) {
        loadPage();
      }
    }
  );
</script>
