<template>
  <AiStackLayout>
    <div class="ai-mt-4 ai-flex ai-w-full ai-flex-col">
      <AiTitle>
        Certidão: {{ certidaoPvlCertidaoStore?.certidao?.modelo?.nome }}
      </AiTitle>

      <div class="ai-mt-4 ai-flex ai-w-full">
        <AiContainer class="ai-mr-4 ai-h-fit ai-w-1/2">
          <AiContainerContent>
            <div
              class="ai-grid ai-grid-cols-4 ai-items-start ai-gap-x-4 ai-gap-y-2"
            >
              <v-text-field
                :model-value="certidaoPvlCertidaoStore?.certidao?.id"
                label="Certidão"
                type="number"
                readonly
                density="compact"
                variant="outlined"
                class="ai-col-span-1"
              />
              <v-text-field
                :model-value="formattedDataCriacao"
                label="Data criação"
                readonly
                density="compact"
                variant="outlined"
                class="ai-col-span-1"
              />
              <v-text-field
                :model-value="certidaoPvlCertidaoStore?.certidao?.situacao"
                label="Situação"
                readonly
                density="compact"
                variant="outlined"
                class="ai-col-span-1"
              />
              <v-text-field
                :model-value="certidaoPvlCertidaoStore?.certidao?.cidade"
                label="Ente"
                type="text"
                readonly
                density="compact"
                variant="outlined"
                class="ai-col-span-1"
              />
              <div class="ai-col-span-4">
                <v-text-field
                  v-model="oficio"
                  label="Ofício"
                  type="text"
                  density="compact"
                  variant="outlined"
                  placeholder="Ex: OF-2025/00123"
                  maxlength="30"
                  counter="30"
                  :clearable="!isCertidaoEmitida"
                  :readonly="isCertidaoEmitida"
                  :rules="[(v) => !!v || 'Informe o Ofício']"
                  @blur="onOficioBlur"
                />
              </div>
            </div>
          </AiContainerContent>

          <AiContainerFooter>
            <AiButton
              @click="recalcularCertidao"
              :disabled="
                isCertidaoEmitida ||
                modalDownloadStore.isDownloading ||
                isRecalculating ||
                isEmitting
              "
            >
              <AiIcon>
                <AiSpinner v-if="isRecalculating" />
                <IconSolarRefreshCircleOutline v-else />
              </AiIcon>
              Recalcular
            </AiButton>

            <AiTooltipProvider :delay-duration="0">
              <AiTooltip>
                <AiTooltipTrigger as-child>
                  <div>
                    <AiButton
                      @click="emitirCertidao"
                      :disabled="isEmitirCertidaoDisabled"
                    >
                      <AiIcon>
                        <AiSpinner v-if="isEmitting" />
                        <IconSolarFileCheckOutline v-else />
                      </AiIcon>
                      Emitir Certidão
                    </AiButton>
                  </div>
                </AiTooltipTrigger>

                <AiTooltipContent
                  v-if="isEmitirCertidaoDisabled && !isCertidaoEmitida"
                >
                  Para emitir Certidão, preencha o campo Ofício e conclua todos
                  os formulários.
                </AiTooltipContent>
              </AiTooltip>
            </AiTooltipProvider>

            <AiButton
              @click="downloadCertidao"
              :disabled="modalDownloadStore.isDownloading || isRecalculating"
            >
              <AiIcon><IconSolarPrinter2Outline /></AiIcon>
              Imprimir
            </AiButton>
          </AiContainerFooter>
        </AiContainer>
      </div>
    </div>
  </AiStackLayout>
</template>

<script lang="ts" setup>
  import { computed, onUnmounted, ref, watch } from "vue";
  import { useRoute, useRouter } from "vue-router";

  import { useUpdateCertidaoPvlOficio } from "@/composables/certidaoPvl/useUpdateCertidaoPvlOficio";
  import { useCertidaoPvlCertidaoStore } from "@/stores/auditor/certidaoPvlCertidaoStore";
  import { useModalDownloadStore } from "@/stores/global/modalDownloadStore";
  import { useCertidaoPvlRecalcular } from "@/composables/certidaoPvl/useCertidaoPvlRecalcular";
  import { useEmitirCertidaoPvl } from "@/composables/certidaoPvl/useEmitirCertidaoPvl";
  import { useDownloadFile } from "@/composables/global/useDownloadFile";
  import { useFormattedDate } from "@/composables/global/useFormattedDate";

  import Toaster from "@/components/Toaster.vue";

  const route = useRoute();
  const router = useRouter();
  const certidaoPvlCertidaoStore = useCertidaoPvlCertidaoStore();
  const modalDownloadStore = useModalDownloadStore();

  const oficio = ref(certidaoPvlCertidaoStore.certidao?.oficio || "");
  const certidaoId = computed(() => Number(route.params.certidaoId));
  const formattedDataCriacao = computed(() => {
    if (!certidaoPvlCertidaoStore.certidao?.data_criacao) return "";

    return useFormattedDate(certidaoPvlCertidaoStore.certidao?.data_criacao);
  });

  const isFormulariosConcluidos = computed(() => {
    return certidaoPvlCertidaoStore.certidao?.situacao === "Concluída";
  });

  const isEmitting = ref(false);
  const isRecalculating = ref(false);

  const downloadCertidao = () => {
    if (modalDownloadStore.isDownloading || isRecalculating.value) return;

    useDownloadFile({
      url: `${location.origin}/api/certidao-pvl/certidao/${certidaoId.value}/download`,
      fileName: `certidao_${certidaoId.value}.pdf`
    });
  };

  const emitirCertidao = async () => {
    if (isEmitting.value || !isFormulariosConcluidos.value) return;

    isEmitting.value = true;

    try {
      await useEmitirCertidaoPvl({
        url: `${location.origin}/api/certidao-pvl/certidao/${certidaoId.value}/emitir`,
        fileName: `certidao_${certidaoId.value}_emitida.pdf`
      });

      await certidaoPvlCertidaoStore.fetch(certidaoId.value);
    } finally {
      isEmitting.value = false;
    }
  };

  const recalcularCertidao = async () => {
    if (
      isCertidaoEmitida.value ||
      modalDownloadStore.isDownloading ||
      isRecalculating.value
    )
      return;

    isRecalculating.value = true;

    const { data, error } = await useCertidaoPvlRecalcular(certidaoId.value);

    isRecalculating.value = false;

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data.message,
        status: "error"
      });
      return;
    }

    if (data.value) {
      Toaster.toast({
        message: (data.value as any).message,
        status: "success"
      });
    }
  };

  watch(
    () => certidaoPvlCertidaoStore.certidao?.oficio,
    (newVal) => {
      oficio.value = newVal;
    },
    { immediate: true }
  );

  const onOficioBlur = async () => {
    if (
      !oficio.value ||
      oficio.value === certidaoPvlCertidaoStore.certidao?.oficio ||
      isCertidaoEmitida.value
    ) {
      return;
    }

    const { data, error } = await useUpdateCertidaoPvlOficio(certidaoId.value, {
      oficio: oficio.value
    });

    if (error?.value) {
      Toaster.toast({
        message: "Erro ao salvar o ofício.",
        status: "error"
      });
    } else {
      certidaoPvlCertidaoStore.certidao.oficio = oficio.value;
      Toaster.toast({
        message: "Ofício salvo com sucesso!",
        status: "success"
      });
    }
  };

  const isCertidaoEmitida = computed(() => {
    return certidaoPvlCertidaoStore.certidao?.situacao === "Emitida";
  });

  const isEmitirCertidaoDisabled = computed(() => {
    return (
      isEmitting.value ||
      modalDownloadStore.isDownloading ||
      isRecalculating.value ||
      !isFormulariosConcluidos.value ||
      !oficio.value
    );
  });

  onUnmounted(() => {
    certidaoPvlCertidaoStore.clear();
  });

  if (isNaN(certidaoId.value)) {
    await router.replace(`/certidao-pvl/certidoes`);
  } else {
    await certidaoPvlCertidaoStore.fetch(certidaoId.value);
  }
</script>
