<template>
  <AiContainer>
    <AiContainerHeader>
      <AiContainerTitle>Versões anteriores</AiContainerTitle>
    </AiContainerHeader>

    <AiContainerContent>
      <AiDataTable
        :data="formularioVersoes"
        :columns="columns"
        :page-count="pageCount"
        :total-count="totalCount"
        :page-size="pageSize"
        :current-page="currentPage"
        @update:pagination="handleUpdatePagination"
      />
    </AiContainerContent>
  </AiContainer>
</template>

<script lang="ts" setup>
  import { columns } from "./certidaoPvlFormularioVersaoListColumns";
  import { computed, ref, watch } from "vue";
  import { useRoute } from "vue-router";

  import { useCertidaoPvlFormularioStore } from "@/stores/auditor/certidaoPvlFormularioStore";
  import { useFetchCertidaoPvlFormularioVersoes } from "@/composables/certidaoPvl/useFetchCertidaoPvlFormularioVersoes";

  import Toaster from "@/components/Toaster.vue";

  import type { CertidaoPvlFormularioVersao } from "@/types/CertidaoPvlFormularioVersao";

  const route = useRoute();
  const certidaoPvlFormularioStore = useCertidaoPvlFormularioStore();

  const certidaoId = computed(() => Number(route.params.certidaoId));
  const formularioId = computed(() => Number(route.params.formularioId));

  const formularioVersoes = ref<CertidaoPvlFormularioVersao[]>([]);
  const pageCount = ref(1);
  const totalCount = ref(0);
  const pageSize = ref(10);
  const currentPage = ref(1);

  const fetchCertidaoPvlFormularioVersoes = async (
    newCurrentPage: number,
    newPageSize: number
  ) => {
    const { data, error } = await useFetchCertidaoPvlFormularioVersoes({
      certidaoId: certidaoId.value,
      formularioId: formularioId.value,
      page: newCurrentPage,
      itemsPerPage: newPageSize
    });

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data.message,
        status: "error"
      });
      return;
    }

    if (data.value?.data) {
      formularioVersoes.value = data.value.data;
      pageCount.value = data.value.meta?.last_page;
      totalCount.value = data.value.meta?.total;
      pageSize.value = data.value.meta?.per_page;
      currentPage.value = data.value.meta?.current_page;
    }
  };

  const handleUpdatePagination = (
    newCurrentPage: number,
    newPageSize: number
  ) => {
    currentPage.value = newCurrentPage;
    pageSize.value = newPageSize;
    fetchCertidaoPvlFormularioVersoes(newCurrentPage, newPageSize);
  };

  watch(
    () => certidaoPvlFormularioStore.formulario,
    (newValue, oldValue) => {
      if (oldValue) {
        handleUpdatePagination(1, pageSize.value);
      }
    },
    { deep: true }
  );

  await fetchCertidaoPvlFormularioVersoes(1, pageSize.value);
</script>
