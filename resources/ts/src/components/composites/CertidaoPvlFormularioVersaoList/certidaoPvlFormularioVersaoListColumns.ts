import router from "@/router/auditor";

import { useFormattedDate } from "@/composables/global/useFormattedDate";

import IconEye from "~icons/solar/eye-outline";

export const columns = [
  {
    key: "versao",
    title: "Vers<PERSON>"
  },
  {
    key: "data_preenchimento",
    title: "Data preenchimento",
    cell: ({ row }: any) =>
      useFormattedDate(row.getValue("data_preenchimento"), "datetime"),
    sortingFn: (rowA: any, rowB: any) => {
      const dateA = new Date(rowA.original.data_preenchimento);
      const dateB = new Date(rowB.original.data_preenchimento);
      return dateA.getTime() - dateB.getTime();
    }
  },
  {
    key: "usuario.name",
    id: "usuarioName",
    title: "Usuário",
    cell: ({ row }: any) => row.getValue("usuarioName")
  },
  {
    id: "actions",
    title: "<PERSON><PERSON><PERSON><PERSON>",
    actions: [
      {
        icon: IconEye,
        label: "Ver",
        action: (row: any) => {
          const route = router.currentRoute.value;
          const certidaoId = route.params.certidaoId;
          const formularioId = row.original.certidao_formulario_id;
          const versaoId = row.original.id;

          router.push(
            `/certidao-pvl/certidao/${certidaoId}/formulario/${formularioId}/versao/${versaoId}`
          );
        }
      }
    ]
  }
];
