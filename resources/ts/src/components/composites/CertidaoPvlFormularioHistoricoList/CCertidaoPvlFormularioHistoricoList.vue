<template>
  <AiContainer>
    <AiContainerHeader>
      <AiContainerTitle>Histórico de alterações</AiContainerTitle>
    </AiContainerHeader>

    <AiContainerContent>
      <AiDataTable
        :data="formularioAcoes"
        :columns="columns"
        :page-count="pageCount"
        :total-count="totalCount"
        :page-size="pageSize"
        :current-page="currentPage"
        @update:pagination="handleUpdatePagination"
      />
    </AiContainerContent>
  </AiContainer>
</template>

<script lang="ts" setup>
  import { columns } from "./certidaoPvlFormularioHistoricoListColumns";
  import { computed, ref, watch } from "vue";
  import { useRoute } from "vue-router";

  import { useCertidaoPvlFormularioStore } from "@/stores/auditor/certidaoPvlFormularioStore";
  import { useFetchCertidaoPvlFormularioAcoes } from "@/composables/certidaoPvl/useFetchCertidaoPvlFormularioAcoes";

  import Toaster from "@/components/Toaster.vue";

  import type { CertidaoPvlFormularioAcao } from "@/types/CertidaoPvlFormularioAcao";

  const route = useRoute();
  const certidaoPvlFormularioStore = useCertidaoPvlFormularioStore();

  const certidaoId = computed(() => Number(route.params.certidaoId));
  const formularioId = computed(() => Number(route.params.formularioId));

  const formularioAcoes = ref<CertidaoPvlFormularioAcao[]>([]);
  const pageCount = ref(1);
  const totalCount = ref(0);
  const pageSize = ref(10);
  const currentPage = ref(1);

  const fetchCertidaoPvlFormularioAcoes = async (
    newCurrentPage: number,
    newPageSize: number
  ) => {
    const { data, error } = await useFetchCertidaoPvlFormularioAcoes({
      certidaoId: certidaoId.value,
      formularioId: formularioId.value,
      page: newCurrentPage,
      itemsPerPage: newPageSize
    });

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data.message,
        status: "error"
      });
      return;
    }

    if (data.value?.data) {
      formularioAcoes.value = data.value.data;
      pageCount.value = data.value.meta?.last_page;
      totalCount.value = data.value.meta?.total;
      pageSize.value = data.value.meta?.per_page;
      currentPage.value = data.value.meta?.current_page;
    }
  };

  const handleUpdatePagination = (
    newCurrentPage: number,
    newPageSize: number
  ) => {
    currentPage.value = newCurrentPage;
    pageSize.value = newPageSize;
    fetchCertidaoPvlFormularioAcoes(newCurrentPage, newPageSize);
  };

  watch(
    () => certidaoPvlFormularioStore.formulario,
    (newValue, oldValue) => {
      if (oldValue) {
        fetchCertidaoPvlFormularioAcoes(1, pageSize.value);
      }
    },
    { deep: true }
  );

  await fetchCertidaoPvlFormularioAcoes(1, pageSize.value);
</script>
