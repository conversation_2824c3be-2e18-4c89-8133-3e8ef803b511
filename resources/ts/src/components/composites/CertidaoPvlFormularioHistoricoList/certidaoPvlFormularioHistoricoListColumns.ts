import { useFormattedDate } from "@/composables/global/useFormattedDate";

export const columns = [
  {
    key: "user.name",
    id: "usuarioName",
    title: "Usuário",
    cell: ({ row }: any) => row.getValue("usuarioName")
  },
  {
    key: "acao_texto",
    title: "Ação"
  },
  {
    key: "data_acao",
    title: "Data",
    cell: ({ row }: any) =>
      useFormattedDate(row.getValue("data_acao"), "datetime"),
    sortingFn: (rowA: any, rowB: any) => {
      const dateA = new Date(rowA.original.data_acao);
      const dateB = new Date(rowB.original.data_acao);
      return dateA.getTime() - dateB.getTime();
    }
  }
];
