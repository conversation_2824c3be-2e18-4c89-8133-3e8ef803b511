<script setup lang="ts">
  import { useFormattedDate } from "@/composables/global/useFormattedDate";
  import { useCertidaoPvlCertidaoStore } from "@/stores/auditor/certidaoPvlCertidaoStore";
  import { useRouter, useRoute } from "vue-router";
  import { computed } from "vue";

  const store = useCertidaoPvlCertidaoStore();
  const router = useRouter();
  const route = useRoute();

  const isVisible = computed(
    () =>
      route.name === "certidao-pvl-certidao" ||
      route.name === "certidao-pvl-certidao-formulario" ||
      route.name === "certidao-pvl-certidao-formulario-versao"
  );
  const certidaoId = computed(() => store.certidao?.id || "---");
  const ente = computed(() => store.certidao?.cidade || "---");
  const dataCriacao = computed(() =>
    store.certidao?.data_criacao
      ? useFormattedDate(store.certidao?.data_criacao)
      : "---"
  );
  const situacao = computed(() => store.certidao?.situacao || "---");

  const handleBackButtonClick = () => {
    router.push({ path: "/certidao-pvl/certidoes" });
  };
</script>
<template>
  <div class="ai-flex ai-flex-col ai-gap-8" v-if="isVisible">
    <div>
      <AiButton variant="outlined" size="sm" @click="handleBackButtonClick">
        <AiIcon><IconSolarAltArrowLeftOutline /></AiIcon>
        Voltar ao menu principal
      </AiButton>
    </div>

    <ul class="ai-m-0 ai-flex ai-flex-col ai-gap-2 ai-p-0">
      <li>
        <strong>Certidão: </strong>
        <span>
          {{ certidaoId }}
        </span>
      </li>

      <li>
        <strong>Data criação: </strong>
        <span>
          {{ dataCriacao }}
        </span>
      </li>

      <li>
        <strong>Situação: </strong>
        <span>
          {{ situacao }}
        </span>
      </li>

      <li>
        <strong>Ente: </strong>
        <span>
          {{ ente }}
        </span>
      </li>
    </ul>
  </div>
</template>
