<template>
  <AiContainer>
    <AiContainerContent>
      <AiDataTable
        :columns="columns"
        :data="certidaoPvlStore.certidoes"
        :page-count="certidaoPvlStore.pageCount"
        :total-count="certidaoPvlStore.totalCount"
        :page-size="certidaoPvlStore.pageSize"
        :current-page="certidaoPvlStore.currentPage"
        @update:pagination="handleUpdatePagination"
      />
    </AiContainerContent>
  </AiContainer>
</template>

<script lang="ts" setup>
  import { watch } from "vue";
  import { columns } from "./certidaoPvlListColumns";
  import { useCertidaoPvlStore } from "@/stores/auditor/certidaoPvlStore";

  import { AiDataTable } from "@bradoctech/ai-ui";

  const certidaoPvlStore = useCertidaoPvlStore();

  const handleUpdatePagination = (
    newCurrenPage: number,
    newPageSize: number
  ) => {
    certidaoPvlStore.updatePagination(newCurrenPage, newPageSize);
  };

  watch(
    () => certidaoPvlStore.filters,
    () => {
      certidaoPvlStore.updatePagination(1, certidaoPvlStore.pageSize);
    },
    { deep: true }
  );

  await certidaoPvlStore.fetch(1, certidaoPvlStore.pageSize);
</script>
