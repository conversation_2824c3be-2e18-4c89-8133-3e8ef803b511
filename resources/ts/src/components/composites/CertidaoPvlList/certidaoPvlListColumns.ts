import router from "@/router/auditor";

import IconEye from "~icons/solar/eye-outline";
import IconPrinter from "~icons/solar/printer-outline";

import { useModalDownloadStore } from "@/stores/global/modalDownloadStore";
import { useDownloadFile } from "@/composables/global/useDownloadFile";
import { useFormattedDate } from "@/composables/global/useFormattedDate";

const modalDownloadStore = useModalDownloadStore();

const downloadCertidao = (certidaoId: number) => {
  useDownloadFile({
    url: `${location.origin}/api/certidao-pvl/certidao/${certidaoId}/download`,
    fileName: `certidao_${certidaoId}.pdf`
  });
};

export const columns = [
  {
    key: "id",
    title: "Certidão"
  },
  {
    key: "data_criacao",
    title: "Data criação",
    cell: ({ row }: any) => {
      return useFormattedDate(row.getValue("data_criacao"));
    },
    sortingFn: (rowA: any, rowB: any) => {
      const dateA = new Date(rowA.original.data_criacao);
      const dateB = new Date(rowB.original.data_criacao);
      return dateA.getTime() - dateB.getTime();
    }
  },
  {
    key: "cidade",
    title: "Município"
  },
  {
    key: "responsavel",
    title: "Responsável"
  },
  {
    key: "situacao",
    title: "Situação"
  },
  {
    id: "actions",
    title: "Ações",
    actions: [
      {
        icon: IconEye,
        label: "Ver",
        action: (row: any) => {
          router.push(`/certidao-pvl/certidao/${row.getValue("id")}`);
        }
      },
      {
        icon: IconPrinter,
        label: "Imprimir",
        action: (row: any) => {
          downloadCertidao(row.original.id);
        },
        disabled: modalDownloadStore.isDownloading,
        loading: modalDownloadStore.isDownloading
      }
    ]
  }
];
