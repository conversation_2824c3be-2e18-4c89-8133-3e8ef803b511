<template>
  <div class="ai-flex ai-w-full ai-items-center ai-justify-end">
    <AiButton
      @click="handleNewCertidaoButtonClick"
      :disabled="isCreatingCertidao"
    >
      <AiIcon>
        <AiSpinner v-if="isCreatingCertidao" />
        <IconSolarAddCircleOutline v-else />
      </AiIcon>
      Nova Certidão
    </AiButton>
  </div>

  <AiDialog v-model:open="dialogCreate">
    <AiDialogContent>
      <AiDialogHeader>
        <AiDialogTitle> Criar Nova Certidão PVL </AiDialogTitle>
      </AiDialogHeader>

      <AiContainer>
        <AiContainerContent>
          <p class="ai-text-base">
            Escolha para qual Ente deseja criar a Certidão:
          </p>

          <AiFieldset>
            <AiCombobox
              v-model="ente"
              label="Município / Estado*"
              :options="certidaoPvlMunicipiosStore?.municipios"
              option-label="text"
              option-value="value"
              clearable
              :error="enteError"
              @blur="validateEnte"
              @update:model-value="validateEnte"
            />
            <AiInput
              v-model="oficio"
              label="Ofício*"
              caption="Ex: OF-2025/00123"
              :error="oficioError"
              :maxlength="30"
              clearable
              @blur="validateOficio"
              @update:model-value="validateOficio"
            />
          </AiFieldset>

          <AiContainer elevation="1">
            <AiContainerContent>
              <AiContentGroup>
                <AiFieldset>
                  <p>
                    Declaração de Instituição, Previsão e Arrecadação de
                    Tributos
                  </p>
                  <AiRadioGroup v-model="isDeclaracaoInstituicao">
                    <AiContentGroup class="flex-row items-center">
                      <AiRadioGroupItem
                        id="isDeclaracaoInstituicao1"
                        value="1"
                      />
                      <AiLabel for="isDeclaracaoInstituicao1">Sim</AiLabel>
                      <AiRadioGroupItem
                        id="isDeclaracaoInstituicao2"
                        value="0"
                      />
                      <AiLabel for="isDeclaracaoInstituicao2">Não</AiLabel>
                    </AiContentGroup>
                  </AiRadioGroup>
                </AiFieldset>

                <AiFieldset>
                  <p>Declaração de Limites e Condições da Legislação Fiscal</p>
                  <AiRadioGroup v-model="isDeclaracaoLimites">
                    <AiContentGroup class="flex-row items-center">
                      <AiRadioGroupItem id="isDeclaracaoLimites1" value="1" />
                      <AiLabel for="isDeclaracaoLimites1">Sim</AiLabel>
                      <AiRadioGroupItem id="isDeclaracaoLimites2" value="0" />
                      <AiLabel for="isDeclaracaoLimites2">Não</AiLabel>
                    </AiContentGroup>
                  </AiRadioGroup>
                </AiFieldset>

                <AiFieldset>
                  <p>Declaração de Operações de Créditos Vedadas</p>
                  <AiRadioGroup v-model="isDeclaracaoOperacaoes">
                    <AiContentGroup class="flex-row items-center">
                      <AiRadioGroupItem
                        id="isDeclaracaoOperacaoes1"
                        value="1"
                      />
                      <AiLabel for="isDeclaracaoOperacaoes1">Sim</AiLabel>
                      <AiRadioGroupItem
                        id="isDeclaracaoOperacaoes2"
                        value="0"
                      />
                      <AiLabel for="isDeclaracaoOperacaoes2">Não</AiLabel>
                    </AiContentGroup>
                  </AiRadioGroup>
                </AiFieldset>
              </AiContentGroup>
            </AiContainerContent>
          </AiContainer>
        </AiContainerContent>
      </AiContainer>

      <AiDialogFooter>
        <AiButton
          variant="outlined"
          @click="dialogCreate = false"
          :disabled="isCreatingCertidao"
        >
          <AiIcon><IconSolarCloseCircleOutline /></AiIcon>
          Cancelar
        </AiButton>
        <AiButton
          @click="handleCreateCertidaoButtonClick"
          :disabled="
            isCreatingCertidao ||
            !ente ||
            !oficio ||
            !isDeclaracaoInstituicao ||
            !isDeclaracaoLimites ||
            !isDeclaracaoOperacaoes
          "
        >
          <AiIcon>
            <AiSpinner v-if="isCreatingCertidao" />
            <IconSolarAddCircleOutline v-else />
          </AiIcon>
          Criar
        </AiButton>
      </AiDialogFooter>
    </AiDialogContent>
  </AiDialog>
</template>

<script setup lang="ts">
  import { useCreateCertidaoPvl } from "@/composables/certidaoPvl/useCreateCertidaoPvl";
  import { onUnmounted, ref, watch } from "vue";
  import { useRouter } from "vue-router";
  import { useCertidaoPvlMunicipiosStore } from "@/stores/auditor/certidaoPvlMunicipiosStore";

  import Toaster from "@/components/Toaster.vue";

  const router = useRouter();
  const certidaoPvlMunicipiosStore = useCertidaoPvlMunicipiosStore();

  const dialogCreate = ref(false);
  const ente = ref();
  const oficio = ref();
  const isCreatingCertidao = ref(false);
  const isDeclaracaoInstituicao = ref();
  const isDeclaracaoLimites = ref();
  const isDeclaracaoOperacaoes = ref();
  const enteError = ref("");
  const oficioError = ref("");

  const validateEnte = () => {
    enteError.value = "";
    if (!ente.value) {
      enteError.value = "Selecione o Ente";
    }
  };

  const validateOficio = () => {
    oficioError.value = "";
    if (!oficio.value) {
      oficioError.value = "Informe o Ofício";
    }
  };

  const handleCreateCertidaoButtonClick = async () => {
    if (isCreatingCertidao.value) {
      return;
    }

    if (
      !ente.value ||
      !oficio.value ||
      !isDeclaracaoInstituicao.value ||
      !isDeclaracaoLimites.value ||
      !isDeclaracaoOperacaoes.value
    ) {
      return;
    }

    isCreatingCertidao.value = true;

    const payload = {
      ug_id: ente.value,
      oficio: oficio.value,
      declaracao_instituicao: isDeclaracaoInstituicao.value,
      declaracao_limites: isDeclaracaoLimites.value,
      declaracao_operacoes: isDeclaracaoOperacaoes.value
    };
    const { data, error } = await useCreateCertidaoPvl(payload);

    isCreatingCertidao.value = false;

    if (error?.value) {
      return;
    }

    Toaster.toast({
      message: "Certidão criada com sucesso!",
      status: "success"
    });

    const certidaoId = data.value?.data.id;

    dialogCreate.value = false;

    router.push({
      path: `/certidao-pvl/certidao/${certidaoId}`
    });
  };

  const handleNewCertidaoButtonClick = () => {
    if (isCreatingCertidao.value) {
      return;
    }

    dialogCreate.value = true;
  };

  onUnmounted(() => {
    certidaoPvlMunicipiosStore.clear();
  });

  watch(
    dialogCreate,
    (newValue) => {
      if (newValue) {
        ente.value = undefined;
        oficio.value = undefined;
        isDeclaracaoInstituicao.value = undefined;
        isDeclaracaoLimites.value = undefined;
        isDeclaracaoOperacaoes.value = undefined;
      }
    },
    { immediate: true }
  );
</script>
