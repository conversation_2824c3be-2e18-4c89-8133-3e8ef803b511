<template>
  <AiContainer>
    <AiContainerHeader>
      <div class="ai-flex ai-items-center ai-justify-between">
        <span class="ai-text-2xl ai-font-semibold">
          {{ certidaoPvlFormularioStore?.formulario?.nome }}
        </span>
        <span class="ai-text-lg ai-font-semibold">
          Versão do documento:
          {{ certidaoPvlFormularioStore?.formulario?.versao }}
        </span>
      </div>
    </AiContainerHeader>

    <AiContainerContent>
      <Suspense>
        <AiCkeditor
          :data="certidaoPvlFormularioStore?.formulario?.texto || ''"
          :disabled="isFinished"
          :autosave-url="autosaveUrl"
          autosave-http-method="PATCH"
          :autosave-data="autosaveData"
          :is-saving="isSaving"
          :fixed-toolbar-top-margin="94"
          @update:data="handleEditorUpdateData"
          @status-change="handleEditorStatusChange"
          @ready="handleEditorReady"
          @recovery="handleEditorRecovery"
        />
      </Suspense>
    </AiContainerContent>

    <AiContainerFooter>
      <AiButton
        variant="outlined"
        @click="saveCertidaoPvlFormulario"
        :disabled="
          isProcessing ||
          isSaving ||
          isFinished ||
          editorStatus.isBusy ||
          !editorStatus.isDirty
        "
      >
        <AiIcon>
          <IconSolarSdCardOutline />
        </AiIcon>
        Salvar
      </AiButton>

      <AiButton
        v-if="isFinished"
        @click="reopenCertidaoPvlFormulario"
        :disabled="
          isProcessing ||
          editorStatus.isBusy ||
          certidaoPvlCertidaoStore?.certidao?.situacao === 'Emitida'
        "
      >
        <AiIcon>
          <IconSolarFolderOpenOutline />
        </AiIcon>
        Reabrir formulário
      </AiButton>

      <AiButton
        v-else
        @click="saveCertidaoPvlFormulario({ saveAndFinish: true })"
        :disabled="
          isProcessing || isSaving || editorStatus.isBusy || !currentEditorData
        "
      >
        <AiIcon>
          <IconSolarUnreadOutline />
        </AiIcon>
        Concluir
      </AiButton>
    </AiContainerFooter>
  </AiContainer>
</template>

<script lang="ts" setup>
  import { useCertidaoPvlCertidaoStore } from "@/stores/auditor/certidaoPvlCertidaoStore";
  import { useCertidaoPvlFormularioStore } from "@/stores/auditor/certidaoPvlFormularioStore";
  import { useMenuCertidaoPvlStore } from "@/stores/auditor/menuCertidaoPvlStore";
  import { useSaveCertidaoPvlFormulario } from "@/composables/certidaoPvl/useSaveCertidaoPvlFormulario";
  import { useReopenCertidaoPvlFormulario } from "@/composables/certidaoPvl/useReopenCertidaoPvlFormulario";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { computed, onUnmounted, ref, watchEffect } from "vue";
  import { useRoute, useRouter } from "vue-router";

  import Toaster from "@/components/Toaster.vue";

  type EditorStatus = {
    isBusy: boolean;
    isDirty: boolean;
  };

  const route = useRoute();
  const router = useRouter();
  const certidaoPvlCertidaoStore = useCertidaoPvlCertidaoStore();
  const certidaoPvlFormularioStore = useCertidaoPvlFormularioStore();
  const menuCertidaoPvlStore = useMenuCertidaoPvlStore();
  const fetchStore = useFetchStore();

  const certidaoId = computed(() => Number(route.params.certidaoId));
  const formularioId = computed(() => Number(route.params.formularioId));
  const isFinished = computed(
    () => certidaoPvlFormularioStore.formulario?.status === "finalizado"
  );
  const autosaveUrl = computed(
    () =>
      `/certidao-pvl/certidao/${certidaoId.value}/formulario/${formularioId.value}/auto-save`
  );
  const autosaveData = computed(() => {
    return certidaoPvlFormularioStore.formulario?.texto_auto_save || "";
  });

  const currentEditorData = ref("");
  const isSaving = ref(false);
  const isProcessing = ref(false);
  const editorStatus = ref<EditorStatus>({ isBusy: false, isDirty: false });

  const handleEditorReady = async () => {
    certidaoPvlFormularioStore.clear();

    if (isNaN(certidaoId.value) || isNaN(formularioId.value)) {
      router.replace("/certidao-pvl/certidoes");
    } else {
      await Promise.all([
        certidaoPvlCertidaoStore.fetch(certidaoId.value),
        certidaoPvlFormularioStore.fetch(certidaoId.value, formularioId.value)
      ]);

      currentEditorData.value =
        certidaoPvlFormularioStore.formulario?.texto || "";
    }

    isProcessing.value = false;
  };

  const handleEditorUpdateData = (data: string) => {
    currentEditorData.value = data;
  };

  const recoveryRetries = ref(0);

  const handleEditorRecovery = (data: string) => {
    recoveryRetries.value += 1;

    if (recoveryRetries.value > 4) {
      Toaster.toast({
        message: "Não foi possível recuperar dados não salvos do editor",
        status: "warning"
      });
      return;
    }

    if (isProcessing.value || isSaving.value) {
      setTimeout(() => {
        handleEditorRecovery(data);
      }, 1000);
    } else {
      currentEditorData.value = data;
    }
  };

  const handleEditorStatusChange = (status: EditorStatus) => {
    editorStatus.value = status;
  };

  const saveCertidaoPvlFormulario = async ({ saveAndFinish = false }) => {
    isSaving.value = true;
    isProcessing.value = true;

    const { data, error } = await useSaveCertidaoPvlFormulario({
      certidaoId: certidaoId.value,
      formularioId: formularioId.value,
      payload: {
        data_preenchimento: new Date().toISOString(),
        texto: currentEditorData.value,
        status: saveAndFinish ? "finalizado" : "iniciado"
      }
    });

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data?.message,
        status: "error"
      });
    } else if (data.value) {
      Toaster.toast({
        message: data.value.data?.message || "Formulário salvo com sucesso",
        status: "success"
      });

      await updateData();
    }

    isSaving.value = false;
    isProcessing.value = false;
  };

  const reopenCertidaoPvlFormulario = async () => {
    if (certidaoPvlCertidaoStore.certidao?.situacao === "Emitida") return;

    isProcessing.value = true;

    const { data, error } = await useReopenCertidaoPvlFormulario({
      certidaoId: certidaoId.value,
      formularioId: formularioId.value
    });

    if (error.value) {
      Toaster.toast({
        message: (error.value as any).data?.message,
        status: "error"
      });
    } else if (data.value) {
      Toaster.toast({
        message: data.value.data?.message || "Formulário reaberto com sucesso",
        status: "success"
      });

      await updateData();
    }

    isProcessing.value = false;
  };

  const updateData = async () => {
    await Promise.all([
      certidaoPvlFormularioStore.fetch(certidaoId.value, formularioId.value),
      menuCertidaoPvlStore.fetchMenu(certidaoId.value)
    ]);
  };

  onUnmounted(() => {
    certidaoPvlCertidaoStore.clear();
    certidaoPvlFormularioStore.clear();
  });

  watchEffect(() => {
    if (isProcessing.value || isSaving.value || editorStatus.value.isBusy) {
      fetchStore.setFetchState("fetching");
    } else {
      fetchStore.setFetchState("done");
    }
  });

  isProcessing.value = true;
</script>
