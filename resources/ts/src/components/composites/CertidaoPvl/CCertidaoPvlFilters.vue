<template>
  <AiContainer>
    <AiContainerHeader>
      <AiContainerTitle>Filtrar</AiContainerTitle>
    </AiContainerHeader>

    <AiContainerContent>
      <div
        class="ai-grid ai-grid-cols-1 ai-grid-rows-[repeat(1,auto)] ai-items-start ai-gap-x-8 ai-gap-y-2 xl:ai-grid-cols-2"
      >
        <div class="ai-grid ai-grid-cols-2 ai-gap-x-5">
          <Date
            :value="certidaoPvlStore.filters.initialDate"
            label="Data criação"
            density="compact"
            variant="outlined"
            @input-date="handleInitialDateInput"
          />

          <Date
            :value="certidaoPvlStore.filters.finalDate"
            label="Até"
            density="compact"
            variant="outlined"
            @input-date="handleFinalDateInput"
          />
        </div>

        <div
          class="ai-grid ai-grid-cols-3 ai-grid-rows-[repeat(1,auto)] ai-items-start ai-gap-x-4 ai-gap-y-2"
        >
          <v-autocomplete
            v-model="certidaoPvlStore.filters.esfera"
            :items="esferas?.data"
            item-title="text"
            item-value="value"
            label="Esfera"
            placeholder="Selecione a esfera"
            multiple
            small-chips
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          ></v-autocomplete>

          <v-autocomplete
            v-model="certidaoPvlStore.filters.municipio"
            :items="certidaoPvlMunicipiosStore.municipios"
            item-title="text"
            item-value="value"
            label="Município"
            multiple
            small-chips
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          ></v-autocomplete>

          <v-autocomplete
            v-model="certidaoPvlStore.filters.situacao"
            :items="situacoes.data"
            item-title="text"
            item-value="value"
            label="Situação"
            multiple
            small-chips
            clearable
            density="compact"
            variant="outlined"
            class="ai-col-span-1"
          ></v-autocomplete>
        </div>
      </div>

      <AiContainerFooter>
        <AiButton variant="outlined" @click="clearFilters">
          <AiIcon><IconSolarEraserOutline /></AiIcon>
          Limpar
        </AiButton>
        <AiButton @click="applyFilter">
          <AiIcon><IconSolarFilterOutline /></AiIcon>
          Aplicar filtro
        </AiButton>
      </AiContainerFooter>
    </AiContainerContent>
  </AiContainer>
</template>

<script setup lang="ts">
  import { useCertidaoPvlStore } from "@/stores/auditor/certidaoPvlStore";
  import { useCertidaoPvlMunicipiosStore } from "@/stores/auditor/certidaoPvlMunicipiosStore";
  import { onUnmounted } from "vue";
  import { useFetchEsferas } from "@/composables/global/useFetchEsferas";
  import { useFetchCertidaoPvlSituacoes } from "@/composables/certidaoPvl/useFetchCertidaoPvlSituacoes";

  const certidaoPvlStore = useCertidaoPvlStore();
  const certidaoPvlMunicipiosStore = useCertidaoPvlMunicipiosStore();

  const clearFilters = () => {
    certidaoPvlStore.clearFilters();
  };

  const applyFilter = () => {
    certidaoPvlStore.fetch(1);
  };

  const handleInitialDateInput = (value: string) => {
    certidaoPvlStore.filters.initialDate = value;
  };

  const handleFinalDateInput = (value: string) => {
    certidaoPvlStore.filters.finalDate = value;
  };

  onUnmounted(() => {
    certidaoPvlStore.clearFilters();
  });

  const [esferasResult, situacoesResult] = await Promise.all([
    useFetchEsferas(),
    useFetchCertidaoPvlSituacoes(),
    certidaoPvlMunicipiosStore.fetch()
  ]);

  const { data: esferas, error: fetchEsferasError } = esferasResult;
  const { data: situacoes, error: fetchSituacoesError } = situacoesResult;

  fetchEsferasError.value && console.error(fetchEsferasError.value);
  fetchSituacoesError.value && console.error(fetchSituacoesError.value);
</script>
