<template>
  <v-text-field
    v-model="dateFormatted"
    :label="label"
    :rules="[rules.isValid]"
    :density="density"
    ref="date"
    :error="error"
    :error-messages="errorMessage"
    v-mask="'##/##/####'"
    @change="() => toIsoDate(dateFormatted)"
    :disabled="disabled"
    clearable
    @click:clear="$emit('input-date', '')"
  >
    <template v-slot:append>
      <v-menu
        ref="dateMenu"
        v-model="dateMenu"
        :close-on-content-click="false"
        transition="scale-transition"
        offset-y
      >
        <template v-slot:activator="{ props }">
          <AiIcon v-bind="props" class="ai-text-2xl">
            <IconSolarCalendarOutline />
          </AiIcon>
        </template>

        <v-date-picker
          ref="datePicker"
          :value="value"
          :max="max"
          :min="min"
          :disabled="disabled"
          @update:modelValue="save"
          @input="dateMenu = false"
        />
      </v-menu>
    </template>
  </v-text-field>
</template>

<script>
  import { mask } from "vue-the-mask";
  import { defineComponent } from "vue";

  export default defineComponent({
    name: "DateComponent",
    directives: { mask },
    data() {
      return {
        dateFormatted: this.formatDate(this.value),
        dateMenu: false,
        error: false,
        errorMessage: "",
        rules: {
          isValid: (v) => {
            const self = this;
            if (v && v.length === 10) {
              const [day, month, year] = v.split("/");
              const date = new Date(`${year}-${month}-${day}` + "T00:00:00");

              if (date.toLocaleString("pt-BR") === "Invalid Date") {
                return "Data Inválida";
              } else {
                const min = new Date(self.min + "T00:00:00");
                const max = new Date(self.max + "T00:00:00");

                if (date < min) {
                  return (
                    "A data deve ser maior/igual a " +
                    min.toLocaleDateString("pt-BR")
                  );
                } else if (date > max) {
                  return (
                    "A data deve ser menor/igual a " +
                    max.toLocaleDateString("pt-BR")
                  );
                } else {
                  return true;
                }
              }
            } else {
              if (!v || v.length === 0) {
                return true;
              } else {
                return "Data Inválida";
              }
            }
          }
        }
      };
    },
    props: {
      value: {
        type: String,
        required: false
      },
      label: String,
      density: {
        type: String,
        required: false
      },
      max: {
        type: String,
        default: "2100-01-01"
      },
      min: {
        type: String,
        default: "1900-01-01"
      },
      disabled: {
        type: Boolean,
        required: false
      }
    },
    watch: {
      value(val) {
        this.dateFormatted = this.formatDate(val);
      },
      dateMenu(val) {
        val && setTimeout(() => (this.$refs.datePicker.activePicker = "YEAR"));
      }
    },
    methods: {
      formatDate(date) {
        if (!date) return null;
        return date;
      },
      save(date) {
        this.dateMenu = false;
        this.$emit(
          "input-date",
          typeof date === "string" ? date : date.toLocaleDateString("pt-BR")
        );
      },
      toIsoDate(dateStr) {
        if (this.$refs.date.isValid && dateStr !== "") {
          this.save(dateStr);
        } else {
          if (dateStr === "") {
            this.save("");
          }
        }
      }
    }
  });
</script>
