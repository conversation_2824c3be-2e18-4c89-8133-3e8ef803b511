<script setup lang="ts">
  import type { Column } from "@tanstack/vue-table";
  import IconArrowDown from "~icons/solar/arrow-down-outline";
  import IconArrowUp from "~icons/solar/arrow-up-outline";
  import IconSort from "~icons/solar/sort-vertical-outline";

  import { cn } from "@/lib/utils";

  interface DataTableColumnHeaderProps {
    column: Column<any>;
    title: string;
  }

  defineOptions({
    inheritAttrs: false
  });

  defineProps<DataTableColumnHeaderProps>();
</script>

<template>
  <div
    v-if="column.getCanSort()"
    :class="cn('ai-flex ai-items-center ai-space-x-2', $attrs.class ?? '')"
  >
    <button
      @click="column.toggleSorting()"
      class="-ai-ml-2 ai-flex ai-items-center ai-gap-2 ai-rounded-sm ai-px-2 ai-py-1 hover:ai-bg-primary/[0.08] hover:ai-text-primary data-[state=open]:ai-bg-primary/[0.8]"
    >
      <span>{{ title }}</span>
      <AiIcon v-if="column.getIsSorted() === 'desc'">
        <IconArrowDown />
      </AiIcon>
      <AiIcon v-else-if="column.getIsSorted() === 'asc'">
        <IconArrowUp />
      </AiIcon>
      <AiIcon v-else>
        <IconSort />
      </AiIcon>
    </button>
  </div>

  <div v-else :class="$attrs.class">
    {{ title }}
  </div>
</template>
