<template>
  <v-app>
    <transition name="fade-in-up">
      <div v-show="isComponentMounted">
        <div v-if="hasRemessas">
          <v-skeleton-loader
            v-show="isLoadingPage"
            type="heading, paragraph, divider, actions"
          ></v-skeleton-loader>

          <KTCard title="" v-show="!isLoadingPage">
            <template v-slot:body v-if="isRemessaInadimplenteAlertVisible">
              <v-alert variant="outlined" type="warning" prominent border="top">
                Análise não pode ser realizada, existe(m) Remessa(s)
                Inadimplente(s).
              </v-alert>
            </template>

            <template v-slot:body v-else>
              <div class="text-center py-3" v-if="!analiseFormulario">
                <v-row>
                  <v-col cols="12">
                    <p>Carregando dados</p>
                    <span class="v-spinner text-primary">
                      <span></span>
                    </span>
                  </v-col>
                </v-row>
              </div>

              <template v-else>
                <div class="mx-auto">
                  <v-card-actions id="top-actions" class="pt-0">
                    <h2 class="mr-auto capitalize">
                      {{ analiseFormulario.nome }}
                    </h2>
                    <h5 class="">
                      versão do documento: {{ analiseFormulario.versao }}
                    </h5>
                  </v-card-actions>
                </div>

                <div class="div-table">
                  <Suspense>
                    <AiCkeditor
                      :data="analiseFormulario.texto"
                      :disabled="isFinished"
                      :autosave-url="autosaveUrl"
                      :autosaveData="autosaveData"
                      :is-saving="editor.isSaving"
                      :fixed-toolbar-top-margin="94.25"
                      @update:data="handleEditorUpdateData"
                      @status-change="handleEditorStatusChange"
                    />
                  </Suspense>
                </div>

                <v-row>
                  <v-col cols="auto" class="me-auto">
                    <v-sheet class="pa-2 ma-2">
                      <div class="h5">
                        {{ analiseFormulario.user_finalizado }}
                      </div>
                    </v-sheet>
                  </v-col>
                  <v-col cols="auto">
                    <v-sheet class="pa-2 ma-2">
                      <v-btn
                        class="mr-1"
                        color="primary"
                        @click="save()"
                        :disabled="
                          isFinished ||
                          processing ||
                          editor.status.isBusy ||
                          !editor.status.isDirty
                        "
                      >
                        <AiIcon>
                          <IconSolarSdCardLinear width="22px" height="22px" />
                        </AiIcon>
                        <span class="ml-1">Salvar</span>
                        <i v-if="processing" class="v-spinner"></i>
                      </v-btn>

                      <v-btn
                        v-if="isFinished"
                        class="mr-1"
                        color="warning"
                        :disabled="processing || editor.status.isBusy"
                        @click="reOpenForm()"
                      >
                        <AiIcon>
                          <IconSolarFolderOpenLinear
                            width="22px"
                            height="22px"
                          />
                        </AiIcon>
                        <span class="ml-1">Reabrir Formulário</span>
                        <i v-if="processing" class="v-spinner"></i>
                      </v-btn>
                      <v-btn
                        v-else
                        class="mr-1"
                        color="secondary"
                        :disabled="processing || editor.status.isBusy"
                        @click="saveAndFinish()"
                      >
                        <AiIcon :color="processingFilter || '#FFFFFF'">
                          <IconSolarUnreadLinear width="22px" height="22px" />
                        </AiIcon>
                        <span class="ml-1">Concluir</span>
                        <i v-if="processing" class="v-spinner"></i>
                      </v-btn>
                    </v-sheet>
                  </v-col>
                </v-row>

                <div class="mt-5">
                  <v-row class="m-0">
                    <v-col class="pl-0 pr-5 align-content-center">
                      <v-skeleton-loader :loading="loadingAnexos" type="text">
                        <div
                          class="d-flex align-center justify-space-between w-100"
                        >
                          <div class="h3 m-0">Anexos</div>

                          <v-btn
                            color="secondary"
                            type="button"
                            data-bs-toggle="modal"
                            :data-bs-target="`#kt_modal_anexo`"
                            :disabled="isFinished || processing"
                          >
                            <AiIcon>
                              <IconMdiAttachment width="22px" height="22px" />
                            </AiIcon>
                            <span class="ml-1">Anexar Documento</span>
                            <i v-if="processing" class="v-spinner"></i>
                          </v-btn>
                        </div>
                      </v-skeleton-loader>
                    </v-col>
                  </v-row>

                  <v-card>
                    <SkeletonTable
                      class="div-table pb-5 mb-5"
                      v-show="loadingAnexos"
                    />
                    <v-data-table-server
                      v-show="!loadingAnexos"
                      :headers="headersAnexo"
                      :items="listaAnexos"
                      v-model:options="dataTableAnexo.pagination"
                      :items-length="dataTableAnexo.totalItems"
                      :items-per-page="dataTableAnexo.pagination.itemsPerPage"
                      @update:options="updateAnexos"
                      no-data-text="Não existem anexos neste formulário"
                      class="custom-table-managingUnit elevation-1"
                    >
                      <!-- Centralizando header separado do body -->
                      <template v-slot:headers>
                        <tr>
                          <th
                            v-for="header in headersAnexo"
                            :key="header.value"
                            class="text-center"
                          >
                            {{ header.title }}
                          </th>
                        </tr>
                      </template>
                      <template v-slot:loading>
                        <div class="p-5">Carregando anexos ...</div>
                      </template>
                      <template v-slot:[`item.created_at`]="{ item }">
                        {{ formatDate(item.created_at) }}
                      </template>
                      <template v-slot:[`item.actions`]="{ item }">
                        <v-btn
                          color="primary"
                          class="mr-2 d-inline-flex"
                          small
                          @click="
                            downloadAnexo(item.analise_formulario_id, item.id)
                          "
                        >
                          <AiIcon>
                            <IconSolarEyeOutline width="22px" height="22px" />
                          </AiIcon>
                          <span class="ml-1">Download</span>
                        </v-btn>

                        <v-btn
                          color="error"
                          class="d-inline-flex"
                          small
                          @click="excluirAnexo(item)"
                          :disabled="isFinished"
                        >
                          <AiIcon
                            ><IconSolarTrashBinMinimalisticOutline
                              width="22px"
                              height="22px"
                          /></AiIcon>

                          <span class="ml-1">Excluir</span>
                        </v-btn>
                      </template>
                    </v-data-table-server>
                  </v-card>
                </div>

                <div class="mt-5">
                  <v-skeleton-loader :loading="loadingVersoes" type="text">
                    <v-row class="m-0">
                      <v-col class="pa-5 pl-0 align-content-center">
                        <div class="h3 m-0">Versões Anteriores</div>
                      </v-col>
                    </v-row>
                  </v-skeleton-loader>
                  <v-card>
                    <SkeletonTable
                      class="div-table pb-5 mb-5"
                      v-show="loadingVersoes"
                    />
                    <v-data-table-server
                      v-show="!loadingVersoes"
                      :headers="headersVersao"
                      :items="listaVersoes"
                      v-model:options="dataTableVersao.pagination"
                      :items-length="dataTableVersao.totalItems"
                      :items-per-page="dataTableVersao.pagination.itemsPerPage"
                      @update:options="updateVersoes"
                      no-data-text="Não existem versões registradas neste formulário"
                      class="custom-table-managingUnit elevation-1"
                    >
                      <!-- Centralizando header separado do body -->
                      <template v-slot:headers>
                        <tr>
                          <th
                            v-for="header in headersVersao"
                            :key="header.value"
                            class="text-center"
                          >
                            {{ header.title }}
                          </th>
                        </tr>
                      </template>
                      <template v-slot:loading>
                        <div class="p-5">Carregando versões ...</div>
                      </template>
                      <template v-slot:[`item.data_preenchimento`]="{ item }">
                        {{ formatDateTime(item.data_preenchimento) }}
                      </template>
                      <template v-slot:[`item.actions`]="{ item }">
                        <v-btn
                          color="primary"
                          class="mr-2 d-inline-flex"
                          small
                          @click="verFormularioVersao(item.id)"
                        >
                          <AiIcon>
                            <IconSolarEyeOutline width="22px" height="22px" />
                          </AiIcon>
                          <span class="ml-1">Ver</span>
                        </v-btn>
                      </template>
                    </v-data-table-server>
                  </v-card>
                </div>

                <div class="mt-5">
                  <v-skeleton-loader :loading="loadingHistorico" type="text">
                    <v-row class="m-0">
                      <v-col class="pa-5 pl-0 align-content-center">
                        <div class="h3 m-0">
                          Histórico de Alterações da Análise
                        </div>
                      </v-col>
                    </v-row>
                  </v-skeleton-loader>
                  <v-card>
                    <SkeletonTable
                      class="div-table pb-5 mb-5"
                      v-show="loadingHistorico"
                    />
                    <v-data-table-server
                      v-show="!loadingHistorico"
                      :headers="headersHistorico"
                      :items="historico"
                      v-model:options="dataTableHistorico.pagination"
                      :items-length="dataTableHistorico.totalItems"
                      :items-per-page="
                        dataTableHistorico.pagination.itemsPerPage
                      "
                      @update:options="updateHistorico"
                      no-data-text="Sem histórico registrado neste formulário"
                      class="custom-table-managingUnit elevation-1"
                    >
                      <!-- Centralizando header separado do body -->
                      <template v-slot:headers>
                        <tr>
                          <th
                            v-for="header in headersHistorico"
                            :key="header.value"
                            class="text-center"
                          >
                            {{ header.title }}
                          </th>
                        </tr>
                      </template>
                      <template v-slot:loading>
                        <div class="p-5">
                          Carregando histórico de alterações da análise ...
                        </div>
                      </template>
                      <template v-slot:[`item.data_acao`]="{ item }">
                        {{ formatDateTime(item.data_acao) }}
                      </template>
                    </v-data-table-server>
                  </v-card>
                </div>
              </template>
            </template>
          </KTCard>
        </div>

        <div v-else>
          <KTCard title="">
            <template v-slot:body>
              <v-alert variant="outlined" type="warning" prominent border="top">
                Análise não pode ser realizada, não existe(m) Remessa(s).
              </v-alert>
            </template>
          </KTCard>
        </div>
      </div>
    </transition>
  </v-app>

  <AnexoModal
    :analiseFormulario="analiseFormulario"
    @uploaded="loadPage"
  ></AnexoModal>
</template>

<script>
  import KTCard from "@/view-metronic-auditor/content/Card.vue";
  import SAPCApi from "@/api/auditor/sapc.js";
  import { VDataTableServer } from "vuetify/components/VDataTable";
  import { defineComponent } from "vue";
  import Toaster from "@/components/Toaster.vue";
  import AnexoModal from "./anexoModal.vue";
  import { EV } from "@/services/auditor/events";
  import { useAnaliseStore } from "@/stores/auditor/analiseStore";
  import SkeletonTable from "@/components/bradoc/SkeletonTable/SkeletonTable.vue";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useMenuSapcStore } from "@/stores/auditor/menuSapcStore";

  const analiseStore = useAnaliseStore();
  const menuSapcStore = useMenuSapcStore();

  export default defineComponent({
    name: "analiseFormulario",
    components: {
      KTCard,
      VDataTableServer,
      AnexoModal,
      SkeletonTable
    },
    props: {
      analiseId: {
        default: null
      },
      formularioId: {
        default: null
      }
    },
    data() {
      return {
        isLoadingPage: false,
        isAnexosFetched: false,
        loading: true,
        loadingAnexos: false,
        loadingVersoes: false,
        loadingHistorico: false,
        isRemessaInadimplenteAlertVisible: false,
        analiseFormulario: {
          id: "",
          nome: "",
          analiseId: ""
        },
        texto: "",
        listaAnexos: [],
        dataTableAnexo: {
          totalItems: 0,
          pagination: {
            page: 1,
            itemsPerPage: 25
          }
        },
        headersAnexo: [
          {
            title: "Número",
            value: "id",
            align: "center",
            sortable: false,
            width: "100px"
          },
          {
            title: "Arquivo",
            value: "arquivo",
            align: "center",
            sortable: true
          },
          {
            title: "Descrição",
            value: "descricao",
            align: "center",
            sortable: true
          },
          {
            title: "Usuário",
            value: "usuario.name",
            align: "center",
            sortable: true
          },
          {
            title: "Ações",
            value: "actions",
            align: "center",
            width: "275px",
            sortable: false
          }
        ],
        listaVersoes: [],
        dataTableVersao: {
          totalItems: 0,
          pagination: {
            page: 1,
            itemsPerPage: 25
          }
        },
        headersVersao: [
          {
            title: "Versão",
            value: "versao",
            align: "center",
            width: "100px",
            sortable: false
          },
          {
            title: "Data Preenchimento",
            value: "data_preenchimento",
            align: "center",
            sortable: true
          },
          {
            title: "Usuário",
            value: "usuario.name",
            align: "center",
            sortable: true
          },
          {
            title: "Ações",
            value: "actions",
            align: "center",
            width: "245px",
            sortable: false
          }
        ],
        historico: [],
        headersHistorico: [
          {
            title: "Usuário",
            value: "usuario.name",
            align: "center",
            width: "200px",
            sortable: false
          },
          {
            title: "Ação",
            value: "acao_texto",
            align: "center",
            sortable: false
          },
          {
            title: "Data",
            value: "data_acao",
            align: "center",
            width: "20%",
            sortable: false
          }
        ],
        dataTableHistorico: {
          totalItems: 0,
          pagination: {
            page: 1,
            itemsPerPage: 25
          }
        },
        editor: {
          status: {
            isBusy: false
          },
          isSaving: false
        },
        processing: false,
        isComponentMounted: false
      };
    },
    async mounted() {
      this.isComponentMounted = true;
      if (!this.analiseId) {
        this.backToAnalises();
        return;
      }

      await this.fetchAnalise();
      await this.fetchRemessas();

      if (!this.hasRemessas) return;

      if (this.hasRemessas && this.hasSomeRemessaInadimplente) {
        this.showRemessaInadimplenteAlert();
        return;
      }

      await this.loadPage();
    },
    methods: {
      updateAnexos(pagination) {
        this.dataTableAnexo.pagination = pagination;

        if (!this.isAnexosFetched) {
          return;
        }

        this.fetchAnexos();
      },
      updateVersoes(pagination) {
        this.dataTableVersao.pagination = pagination;
        this.fetchVersoes();
      },
      updateHistorico(pagination) {
        this.dataTableHistorico.pagination = pagination;
        this.fetchHistorico();
      },
      async fetchRemessas() {
        try {
          if (!this.hasRemessas)
            await analiseStore.fetchRemessas(this.analiseId);
        } catch (error) {
          console.error(error);
          this.handleError(error);
        }
      },
      async fetchAnalise() {
        try {
          if (!this.hasAnalise) await analiseStore.fetchAnalise(this.analiseId);
        } catch (error) {
          console.error(error);
          this.handleError(error);
        }
      },
      showRemessaInadimplenteAlert() {
        this.isRemessaInadimplenteAlertVisible = true;
      },
      backToAnalises() {
        this.$router.push("/e-contas/analises");
      },
      async loadPage() {
        if (!this.analiseId) {
          this.backToAnalises();
          return;
        }

        useFetchStore().setFetchState("fetching");
        this.showLoadingPageSkeleton();

        try {
          await this.fetchData();
          await this.fetchAnexos();
          await this.fetchVersoes();
          await this.fetchHistorico();
          useFetchStore().setFetchState("done");
        } catch (error) {
          useFetchStore().setFetchState("error");
          console.error(error);
        }

        this.hideLoadingPageSkeleton();
      },
      verFormularioVersao(formularioVersaoId) {
        this.$router.push(
          `/e-contas/analise/${this.analiseId}/formulario/${this.formularioId}/versao/${formularioVersaoId}`
        );
      },
      async fetchData() {
        this.loading = true;
        try {
          const objAnalise = await SAPCApi.fetchAnaliseFormulario(
            this.analiseId,
            this.formularioId
          );
          this.loading = false;
          this.analiseFormulario = objAnalise.data.data;
          this.texto = this.analiseFormulario.texto;
        } catch (error) {
          this.loading = false;
          console.error(error);
        }
      },
      async fetchAnexos() {
        this.loadingAnexos = true;
        this.processing = true;

        const filterAnexo = {
          analiseId: this.analiseId,
          formularioId: this.formularioId
        };

        try {
          const success = await SAPCApi.fetchAnexos(
            this.dataTableAnexo.pagination,
            filterAnexo
          );

          this.isAnexosFetched = true;
          this.listaAnexos = success.data.data;
          this.loadingAnexos = false;
          this.processing = false;
          const meta = success.data;
          if (!meta) return;
          this.dataTableAnexo.pagination.page = meta.current_page;
          this.dataTableAnexo.pagination.itemsPerPage = parseInt(meta.per_page);
          this.dataTableAnexo.totalItems = meta.total;
        } catch (error) {
          this.loadingAnexos = false;
          this.processing = false;
          this.handleError(error);
          console.error(error);
        }
      },
      async fetchVersoes() {
        this.loadingVersoes = true;

        const filterVersao = {
          analiseId: this.analiseId,
          formularioId: this.formularioId
        };

        try {
          const success = await SAPCApi.fetchVersoes(
            this.dataTableVersao.pagination,
            filterVersao
          );

          this.listaVersoes = success.data.data;
          this.loadingVersoes = false;
          const meta = success.data;
          if (!meta) return;
          this.dataTableVersao.pagination.page = meta.current_page;
          this.dataTableVersao.pagination.itemsPerPage = parseInt(
            meta.per_page
          );
          this.dataTableVersao.totalItems = meta.total;
        } catch (error) {
          this.loadingVersoes = false;
          this.handleError(error);
          console.error(error);
        }
      },
      async fetchHistorico() {
        this.loadingHistorico = true;

        const filterHistorico = {
          analiseId: this.analiseId,
          formularioId: this.formularioId
        };

        try {
          const success = await SAPCApi.fetchHistorico(
            this.dataTableHistorico.pagination,
            filterHistorico
          );

          this.historico = success.data.data;
          const meta = success.data;
          if (!meta) return;
          this.dataTableHistorico.pagination.page = meta.current_page;
          this.dataTableHistorico.pagination.itemsPerPage = parseInt(
            meta.per_page
          );
          this.dataTableHistorico.totalItems = meta.total;
          this.loadingHistorico = false;
        } catch (error) {
          this.loadingHistorico = false;
          this.handleError(error);
          console.error(error);
        }
      },
      save() {
        const analiseFormularioParam = {
          formularioId: this.formularioId,
          texto: this.texto,
          status: "iniciado"
        };

        this.processing = true;
        this.editor.isSaving = true;

        SAPCApi.saveAnaliseFormulario(analiseFormularioParam)
          .then(() => {
            Toaster.toast({
              message: "Formulário salvo com sucesso!",
              status: "info"
            });
            EV.emit("update-menu"); // Manter compatibilidade com menu do Metronic
            menuSapcStore.fetchMenu(this.analiseId); // Usado com o template TDefault
            this.loadPage();
            this.loading = false;
            this.processing = false;
            this.editor.isSaving = false;
          })
          .catch((error) => {
            Toaster.toast({
              message: error.data.message
                ? error.data.message
                : error.response.data.message,
              status: "warn"
            });
            this.processing = false;
            this.editor.isSaving = false;
          });
      },
      saveAndFinish() {
        const analiseFormularioParam = {
          formularioId: this.formularioId,
          texto: this.texto,
          status: "finalizado"
        };

        this.processing = true;
        this.editor.isSaving = true;

        SAPCApi.saveAnaliseFormulario(analiseFormularioParam)
          .then(() => {
            Toaster.toast({
              message: "Formulário salvo com sucesso!",
              status: "info"
            });
            EV.emit("update-menu"); // Manter compatibilidade com menu do Metronic
            menuSapcStore.fetchMenu(this.analiseId); // Usado com o template TDefault
            this.loadPage();
            this.loading = false;
            this.processing = false;
            this.editor.isSaving = false;
          })
          .catch((error) => {
            Toaster.toast({
              message: error.data.message
                ? error.data.message
                : error.response.data.message,
              status: "warn"
            });
            this.processing = false;
            this.editor.isSaving = false;
          });
      },
      reOpenForm() {
        const analiseFormularioParam = {
          analiseId: this.analiseId,
          formularioId: this.formularioId
        };

        this.processing = true;

        SAPCApi.reabrirAnaliseFormulario(analiseFormularioParam)
          .then(() => {
            Toaster.toast({
              message: "Formulário reaberto com sucesso!",
              status: "info"
            });
            EV.emit("update-menu"); // Manter compatibilidade com menu do Metronic
            menuSapcStore.fetchMenu(this.analiseId); // Usado com o template TDefault
            this.loadPage();
            this.loading = false;
            this.processing = false;
          })
          .catch((error) => {
            Toaster.toast({
              message: error.data.message ?? error.response.data.message,
              status: "warn"
            });
            this.processing = false;
          });
      },
      downloadAnexo(analiseFormularioId, anexoId) {
        SAPCApi.downloadAnexo(this.analiseId, analiseFormularioId, anexoId);
      },
      async excluirAnexo(anexo) {
        const answer = await this.$confirm({
          content: `Deseja excluir o anexo: ${anexo.descricao} - ${anexo.arquivo} ?`,
          title: "Excluir anexo",
          dialogProps: {
            actions: {
              false: "Não",
              true: {
                color: "primary",
                text: "Sim"
              }
            },
            position: "top-center",
            timeout: 5000,
            width: "600",
            persistent: true
          }
        });

        if (!answer) return;

        SAPCApi.excluirAnexo(anexo.id)
          .then(() => {
            Toaster.toast({
              message: "Anexo excluído com sucesso!",
              status: "success"
            });
            this.loadPage();
          })
          .catch(this.handleError);
      },
      handleError(err) {
        const error = err.message ?? err;
        if (error) {
          Toaster.toast({
            message: err.message,
            status: "warn"
          });
        }
      },
      formatDate(value) {
        const d = new Date(value);
        return d.toLocaleString("pt-BR").substr(0, 10);
      },
      formatDateTime(value) {
        const d = new Date(value);
        return d.toLocaleString("pt-BR");
      },
      showLoadingPageSkeleton() {
        this.isLoadingPage = true;
      },
      hideLoadingPageSkeleton() {
        this.isLoadingPage = false;
      },
      handleEditorStatusChange(status) {
        this.editor.status = status;
      },
      handleEditorUpdateData(data) {
        this.texto = data;
      }
    },
    watch: {
      "$route.params.formularioId": {
        handler() {
          // necessario timeout para o vue conseguir atualizar as props
          setTimeout(() => {
            if (this.hasRemessas && !this.hasSomeRemessaInadimplente)
              this.loadPage();
          }, 500);
        },
        immediate: false,
        deep: false
      }
    },
    computed: {
      autosaveUrl() {
        return `/sapc/analise/${this.analiseId}/formulario/${this.formularioId}/auto-save`;
      },
      autosaveData() {
        return this.analiseFormulario.texto_auto_save || "";
      },
      isFinished() {
        return this.analiseFormulario.status === "finalizado";
      },
      remessas() {
        return analiseStore.remessas;
      },
      hasSomeRemessaInadimplente() {
        return this.remessas
          ? this.remessas.some((remessa) => {
              return (
                remessa.status === "waiting-file" ||
                remessa.status === "user-canceled"
              );
            })
          : false;
      },
      hasRemessas() {
        return this.remessas ? this.remessas.length > 0 : false;
      },
      hasAnalise() {
        return analiseStore.analise ? true : false;
      }
    }
  });
</script>

<style lang="scss" scoped>
  .title {
    margin-bottom: 20px;
    padding-left: 5px;
    padding-right: 5px;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
  }
  .div-table {
    margin-top: 20px;
  }
</style>
