import { defineStore } from "pinia";
import { ref } from "vue";
import type { ListOption } from "@/types/ListOption";
import { useFetchCertidaoPvlMunicipios } from "@/composables/certidaoPvl/useFetchCertidaoPvlMunicipios";

export const useCertidaoPvlMunicipiosStore = defineStore(
  "certidao-pvl-municipios",
  () => {
    const municipios = ref<ListOption[]>([]);
    const isFetching = ref(false);
    const fetchError = ref<Error | null>(null);

    const fetch = async () => {
      isFetching.value = true;
      const { data, error } = await useFetchCertidaoPvlMunicipios();
      isFetching.value = false;

      if (data.value?.data) {
        municipios.value = data.value?.data;
      }

      if (error.value) {
        fetchError.value = error.value;
      }
    };

    const clear = () => {
      municipios.value = [];
    };

    return {
      municipios,
      fetchError,
      isFetching,
      fetch,
      clear
    };
  }
);
