import { defineStore } from "pinia";
import { ref } from "vue";
import { useFetchCertidaoPvlFormulario } from "@/composables/certidaoPvl/useFetchCertidaoPvlFormulario";

export const useCertidaoPvlFormularioStore = defineStore(
  "certidao-pvl-formulario",
  () => {
    const formulario = ref();
    const isLoading = ref(false);
    const fetchError = ref();

    const fetch = async (
      certidaoId: number,
      formularioId: number,
      defaultValue: any = null
    ) => {
      isLoading.value = true;

      const { data, error } = await useFetchCertidaoPvlFormulario({
        certidaoId,
        formularioId,
        defaultValue
      });

      error.value && (fetchError.value = error.value);
      data.value && (formulario.value = data.value.data);

      isLoading.value = false;
    };

    const clear = () => {
      formulario.value = undefined;
    };

    return {
      formulario,
      isLoading,
      fetchError,
      fetch,
      clear
    };
  }
);
