import { defineStore } from "pinia";
import { ref } from "vue";
import { useFetchCertidaoPvlCertidao } from "@/composables/certidaoPvl/useFetchCertidaoPvlCertidao";

import type { CertidaoPvl } from "@/types/CertidaoPvl";

export const useCertidaoPvlCertidaoStore = defineStore(
  "certidao-pvl-certidao",
  () => {
    const certidao = ref<CertidaoPvl>();
    const isLoading = ref(false);
    const fetchError = ref();

    const fetch = async (certidaoId: number, defaultValue: any = null) => {
      isLoading.value = true;

      const { data, error } = await useFetchCertidaoPvlCertidao({
        certidaoId,
        defaultValue
      });

      error.value && (error.value = fetchError.value);
      data.value && (certidao.value = data.value.data);

      isLoading.value = false;
    };

    const clear = () => {
      certidao.value = undefined;
    };

    return {
      certidao,
      isLoading,
      fetch,
      clear
    };
  }
);
