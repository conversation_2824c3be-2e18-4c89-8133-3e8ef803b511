import { defineStore } from "pinia";
import { ref } from "vue";

import IconSolarHomeAngleBoldDuotone from "~icons/solar/home-angle-outline";
import IconSolarClockCircleBoldDuotone from "~icons/solar/clock-circle-outline";
import IconSolarStopCircleBoldDuotone from "~icons/solar/stop-circle-outline";
import IconSolarCheckCircleOutline from "~icons/solar/check-circle-outline";
import IconSolarFileLeftBoldDuotone from "~icons/solar/file-left-outline";
import { useFetchCertidaoPvlFormularios } from "@/composables/certidaoPvl/useFetchCertidaoPvlFormularios";

export interface MenuItem {
  type: string;
  heading?: string;
  sectionTitle?: string;
  route?: string;
  items?: Array<MenuItem>;
  icon?: any;
  iconClasses?: string;
  sub?: Array<MenuItem>;
  external?: boolean;
  status?: string;
  statusText?: string;
  action?: () => void;
}

export const useMenuCertidaoPvlStore = defineStore("menu-certidao-pvl", () => {
  const mainMenuItens = ref<Array<MenuItem>>([]);

  const setMainMenuItens = (config: any) => {
    mainMenuItens.value = config;
  };

  const statusTextMap: { [key: string]: string } = {
    aguardando_inicio: "Aguardando início",
    finalizado: "Concluído"
  };

  const getMenuItemStatusText = (status: string) => {
    if (!status || typeof status !== "string") return "";
    return statusTextMap[status] || status;
  };

  const iconMap = {
    aguardando_inicio: IconSolarClockCircleBoldDuotone,
    iniciado: IconSolarStopCircleBoldDuotone,
    finalizado: IconSolarCheckCircleOutline,
    reaberto: IconSolarFileLeftBoldDuotone
  };

  const iconClassesMap = {
    aguardando_inicio: "text-info",
    iniciado: "text-warning",
    finalizado: "text-success",
    reaberto: "text-warning"
  };

  interface BackendItem {
    id: string;
    nome_formulario: string;
    status: keyof typeof iconMap;
  }

  const getMenuConfig = (data: BackendItem[], certidaoId: number) => {
    const menuConfig: Array<MenuItem> = [
      {
        type: "heading",
        heading: "Formulários"
      },
      {
        type: "item",
        heading: "Início",
        route: `/certidao-pvl/certidao/${certidaoId}`,
        icon: IconSolarHomeAngleBoldDuotone
      }
    ];

    data.forEach((item: BackendItem) => {
      menuConfig.push({
        type: "item",
        heading: item.nome_formulario,
        route: `/certidao-pvl/certidao/${certidaoId}/formulario/${item.id}`,
        status: item.status,
        statusText: getMenuItemStatusText(item.status),
        icon: iconMap[item.status],
        iconClasses: iconClassesMap[item.status],
        sectionTitle: item.nome_formulario
      });
    });

    return menuConfig;
  };

  const fetchMenu = async (certidaoId: number) => {
    try {
      const { data, error } = await useFetchCertidaoPvlFormularios(certidaoId);

      // ToDo: tratar erro
      if (error.value) {
        console.error("Erro ao buscar formulários:", error.value);
        return;
      }

      if (data.value?.data) {
        const menuConfig = getMenuConfig(data.value.data, certidaoId);
        setMainMenuItens(menuConfig);
      }
      return;
    } catch (error) {
      console.error(error);
    }
  };

  return {
    mainMenuItens,
    fetchMenu
  };
});
