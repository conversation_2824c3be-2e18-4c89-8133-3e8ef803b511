import { defineStore } from "pinia";
import { ref } from "vue";

import SAPCApi from "@/api/auditor/sapc.js";

import IconSolarHomeAngleBoldDuotone from "~icons/solar/home-angle-outline";
import IconSolarClockCircleBoldDuotone from "~icons/solar/clock-circle-outline";
import IconSolarStopCircleBoldDuotone from "~icons/solar/stop-circle-outline";
import IconSolarCheckCircleOutline from "~icons/solar/check-circle-outline";
import IconSolarFileLeftBoldDuotone from "~icons/solar/file-left-outline";
import UsersApi from "@/api/jurisdicionado/users";
import type { User } from "@/stores/jurisdicionado/authStore";

export interface MenuItem {
  type: string;
  heading?: string;
  sectionTitle?: string;
  route?: string;
  items?: Array<MenuItem>;
  icon?: any;
  iconClasses?: string;
  sub?: Array<MenuItem>;
  external?: boolean;
  status?: string;
  statusText?: string;
  action?: () => void;
}

type UserData = User["data"];

export const useMenuSapcStore = defineStore("menu-sapc", () => {
  const mainMenuItens = ref<Array<MenuItem>>([]);

  const setMainMenuItens = async (config: any[]) => {
    const {
      data: { data: user }
    } = await UsersApi.me<User>();
    mainMenuItens.value = filterMenuByPermissions(config, user);
  };

  const statusTextMap: { [key: string]: string } = {
    aguardando_inicio: "Aguardando início",
    finalizado: "Concluído"
  };

  const getMenuItemStatusText = (status: string) => {
    if (!status || typeof status !== "string") return "";
    return statusTextMap[status] || status;
  };

  function filterMenuByPermissions(config: any[], user: UserData): any[] {
    const permissionRules: Record<string, (user: UserData) => boolean> = {
      "/personificar": (u) => u.can_impersonate
    };

    return config.filter((item) => {
      if (item.type !== "item") return true;

      const rule = permissionRules[item.route];
      if (!rule) return true;

      return rule(user);
    });
  }

  const iconMap = {
    aguardando_inicio: IconSolarClockCircleBoldDuotone,
    iniciado: IconSolarStopCircleBoldDuotone,
    finalizado: IconSolarCheckCircleOutline,
    reaberto: IconSolarFileLeftBoldDuotone
  };

  const iconClassesMap = {
    aguardando_inicio: "text-info",
    iniciado: "text-warning",
    finalizado: "text-success",
    reaberto: "text-warning"
  };

  interface BackendItem {
    id: string;
    nome: string;
    status: keyof typeof iconMap;
  }

  const getMainMenuSapcConfig = (data: BackendItem[], analiseId: number) => {
    const menuConfig: Array<MenuItem> = [
      {
        type: "heading",
        heading: "Formulários"
      },
      {
        type: "item",
        heading: "Início",
        route: `/e-contas/analise/${analiseId}`,
        icon: IconSolarHomeAngleBoldDuotone
      }
    ];

    data.forEach((item: BackendItem) => {
      menuConfig.push({
        type: "item",
        heading: item.nome,
        route: `/e-contas/analise/${analiseId}/formulario/${item.id}`,
        status: item.status,
        statusText: getMenuItemStatusText(item.status),
        icon: iconMap[item.status],
        iconClasses: iconClassesMap[item.status],
        sectionTitle: item.nome
      });
    });

    return menuConfig;
  };

  const fetchMenu = async (analiseId: number) => {
    try {
      const {
        data: { data }
      } = await SAPCApi.fetchFormularios(analiseId);

      const mainMenuSapcConfig = getMainMenuSapcConfig(data, analiseId);

      setMainMenuItens(mainMenuSapcConfig);
      return;
    } catch (error) {
      console.error(error);
    }
  };

  return {
    mainMenuItens,
    fetchMenu
  };
});
