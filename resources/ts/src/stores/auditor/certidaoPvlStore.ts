import { defineStore } from "pinia";
import { computed, ref } from "vue";
import type { CertidaoPvl } from "@/types/CertidaoPvl";
import type { CertidaoPvlFilters } from "@/types/CertidaoPvlFilters";
import { useFetchCertidaoPvlCertidoes } from "@/composables/certidaoPvl/useFetchCertidaoPvlCertidoes";

export const useCertidaoPvlStore = defineStore("certidaoPvl", () => {
  const certidoes = ref<CertidaoPvl[]>([]);
  const pageCount = ref(1);
  const totalCount = ref(0);
  const pageSize = ref(10);
  const currentPage = ref(1);
  const filters = ref<CertidaoPvlFilters>({
    initialDate: "",
    finalDate: "",
    esfera: [],
    municipio: [],
    situacao: []
  });

  const convertDateToISO = (date: string) => {
    const [day, month, year] = date.split("/");
    return `${year}-${month}-${day}`;
  };

  const normalizedFilters = computed(() => {
    return {
      data_inicio: filters.value.initialDate
        ? convertDateToISO(filters.value.initialDate)
        : "",
      data_fim: filters.value.finalDate
        ? convertDateToISO(filters.value.finalDate)
        : "",
      esfera: filters.value.esfera,
      municipio: filters.value.municipio,
      situacao: filters.value.situacao
    };
  });

  const updatePagination = async (
    newCurrentPage: number,
    newPageSize: number
  ) => {
    currentPage.value = newCurrentPage;
    pageSize.value = newPageSize;
    fetch(newCurrentPage, newPageSize);
  };

  const fetch = async (
    page: number = currentPage.value,
    itemsPerPage: number = pageSize.value,
    defaultValue: any = null
  ) => {
    const { data, error: fetchError } = await useFetchCertidaoPvlCertidoes({
      page,
      itemsPerPage,
      filters: normalizedFilters.value,
      defaultValue
    });

    if (data.value?.data) {
      certidoes.value = data.value?.data;
      pageCount.value = data.value?.meta.last_page;
      totalCount.value = data.value?.meta.total;
      currentPage.value = data.value?.meta.current_page;
      pageSize.value = data.value?.meta.per_page;
    }

    if (fetchError.value) {
      throw fetchError.value;
    }
  };

  const clearFilters = () => {
    filters.value = {
      initialDate: "",
      finalDate: "",
      esfera: [],
      municipio: [],
      situacao: []
    };
  };

  return {
    certidoes,
    pageCount,
    totalCount,
    pageSize,
    currentPage,
    filters,
    fetch,
    clearFilters,
    updatePagination
  };
});
