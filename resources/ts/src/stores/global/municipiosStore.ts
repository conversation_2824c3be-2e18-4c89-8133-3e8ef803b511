import { defineStore } from "pinia";
import { ref } from "vue";
import type { ListOption } from "@/types/ListOption";
import { useFetchMunicipios } from "@/composables/global/useFetchMunicipios";

export const useMunicipiosStore = defineStore("municipios", () => {
  const municipios = ref<ListOption[]>([]);
  const isFetching = ref(false);
  const fetchError = ref<Error | null>(null);

  const fetch = async () => {
    isFetching.value = true;
    const { data, error } = await useFetchMunicipios();
    isFetching.value = false;

    if (data.value?.data) {
      municipios.value = data.value?.data;
    }

    if (error.value) {
      fetchError.value = error.value;
    }
  };

  return {
    municipios,
    fetchError,
    isFetching,
    fetch
  };
});
