import { computed, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { useMenuSapcStore } from "@/stores/auditor/menuSapcStore";
import { useMenuCertidaoPvlStore } from "@/stores/auditor/menuCertidaoPvlStore";

import MainMenuAuditorConfig from "@/config/MainMenuAuditorConfig";
import MainMenuJurisdicionadoConfig from "@/config/MainMenuJurisdicionadoConfig";

export function useMainMenuConfig() {
  const route = useRoute();
  const menuSapcStore = useMenuSapcStore();
  const menuCertidaoPvlStore = useMenuCertidaoPvlStore();

  const analiseId = computed(() => Number(route.params.analiseId));
  const certidaoId = computed(() => Number(route.params.certidaoId));

  const isPortalAuditor = computed(() => {
    return location.pathname.includes("/portal-auditor");
  });

  const isSapc = computed(() => {
    return route.path.includes("/e-contas/analise/");
  });

  const isCertidaoPvl = computed(() => {
    return route.path.includes("/certidao-pvl/certidao/");
  });

  const mainMenuItens = computed(() => {
    if (isSapc.value) {
      return menuSapcStore.mainMenuItens;
    }

    if (isCertidaoPvl.value) {
      return menuCertidaoPvlStore.mainMenuItens;
    }

    if (isPortalAuditor.value) {
      return MainMenuAuditorConfig;
    }

    return MainMenuJurisdicionadoConfig;
  });

  watchEffect(async () => {
    if (isSapc.value) {
      !isNaN(analiseId.value) && menuSapcStore.fetchMenu(analiseId.value);
      return;
    }

    if (isCertidaoPvl.value) {
      !isNaN(certidaoId.value) &&
        menuCertidaoPvlStore.fetchMenu(certidaoId.value);
      return;
    }
  });

  return {
    isPortalAuditor,
    isSapc,
    isCertidaoPvl,
    mainMenuItens
  };
}
