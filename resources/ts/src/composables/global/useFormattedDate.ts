export const useFormattedDate = (
  dateString: string,
  format?: "date" | "datetime"
): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("pt-BR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: format === "datetime" ? "2-digit" : undefined,
    minute: format === "datetime" ? "2-digit" : undefined,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
  }).format(date);
};
