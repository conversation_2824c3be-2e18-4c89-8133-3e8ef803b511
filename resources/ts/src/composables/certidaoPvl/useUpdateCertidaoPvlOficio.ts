import { useFetch } from "@/composables/global/useFetch";

export async function useUpdateCertidaoPvlOficio(certidaoId: any, payload: any, defaultValue: any = null) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/atualizar-oficio`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "patch", url: requestUrl, data: payload });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
