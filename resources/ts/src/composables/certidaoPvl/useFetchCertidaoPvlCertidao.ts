import { useFetch } from "@/composables/global/useFetch";

type Params = {
  certidaoId: number;
  defaultValue?: any;
};

export async function useFetchCertidaoPvlCertidao({
  certidaoId,
  defaultValue = null
}: Params) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({
    method: "get",
    url: requestUrl
  });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
