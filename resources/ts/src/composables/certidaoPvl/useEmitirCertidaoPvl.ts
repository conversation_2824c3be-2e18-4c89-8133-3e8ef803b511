import { useFetch } from "@/composables/global/useFetch";
import { useModalDownloadStore } from "@/stores/global/modalDownloadStore";

const modalDownloadStore = useModalDownloadStore();
type Params = {
  url: string;
  fileName?: string;
  mimeType?: string;
};
export async function useEmitirCertidaoPvl({
  url,
  fileName = "documento.pdf",
  mimeType = "application/pdf"
}: Params) {
  modalDownloadStore.isDownloadModalVisible = true;
  modalDownloadStore.isDownloading = true;

  try {
    const { data, error, execute } = useFetch();

    await execute({
      method: "patch",
      url,
      responseType: "arraybuffer"
    });

    if (error.value) {
      throw new Error("Failed to download");
    }

    modalDownloadStore.isDownloading = false;

    setTimeout(() => {
      const blob = new Blob([data.value], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");

      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();

      modalDownloadStore.isDownloadError = false;
      modalDownloadStore.isDownloadModalVisible = false;
      modalDownloadStore.isDownloading = false;
    }, 1000);
  } catch (error) {
    console.error(error);
    modalDownloadStore.isDownloadError = true;
    modalDownloadStore.isDownloadModalVisible = false;
    modalDownloadStore.isDownloading = false;
  }
}
