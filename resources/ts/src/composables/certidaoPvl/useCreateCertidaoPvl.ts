import { useFetch } from "@/composables/global/useFetch";

export async function useCreateCertidaoPvl(payload: any, defaultValue: any = null) {
  const requestUrl = "/certidao-pvl/certidao/criar";

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "post", url: requestUrl, data: payload });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
