import { useFetch } from "@/composables/global/useFetch";

export async function useCertidaoPvlRecalcular(
  certidaoId: number,
  defaultValue: any = null
) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/recalcular`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "post", url: requestUrl, data: defaultValue  });

  return { data, error, isReady, isLoading };
}
