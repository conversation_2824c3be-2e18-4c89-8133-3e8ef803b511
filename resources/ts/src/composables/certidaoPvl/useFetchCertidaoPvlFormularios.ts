import { useFetch } from "@/composables/global/useFetch";

export async function useFetchCertidaoPvlFormularios(
  certidaoId: number,
  filter: Record<string, any> = {},
  defaultValue: any = { data: [], meta: {} }
) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/formularios`;
  const queryParams = new URLSearchParams(filter);
  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "get", url: requestUrl, params: queryParams });

  return { data, error, isReady, isLoading };
}
