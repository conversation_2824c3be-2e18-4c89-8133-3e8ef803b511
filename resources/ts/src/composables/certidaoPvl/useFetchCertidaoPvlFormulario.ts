import { useFetch } from "@/composables/global/useFetch";

type Params = {
  certidaoId: number;
  formularioId: number;
  defaultValue?: any;
};

export async function useFetchCertidaoPvlFormulario({
  certidaoId,
  formularioId,
  defaultValue = null
}: Params) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/formulario/${formularioId}`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "get", url: requestUrl });

  return { data, error, isReady, isLoading };
}
