import { useFetch } from "@/composables/global/useFetch";

type Params = {
  certidaoId: number;
  formularioId: number;
  payload: {
    data_preenchimento: string;
    texto: string;
    status: string;
  };
  defaultValue?: any;
};

export async function useSaveCertidaoPvlFormulario({
  certidaoId,
  formularioId,
  payload,
  defaultValue = null
}: Params) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/formulario/${formularioId}`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({
    method: "post",
    url: requestUrl,
    data: payload
  });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
