import { useFetch } from "@/composables/global/useFetch";

type Params = {
  certidaoId: number;
  formularioId: number;
  defaultValue?: any;
};

export async function useReopenCertidaoPvlFormulario({
  certidaoId,
  formularioId,
  defaultValue = null
}: Params) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/formulario/${formularioId}/reabrir`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({
    method: "patch",
    url: requestUrl
  });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
