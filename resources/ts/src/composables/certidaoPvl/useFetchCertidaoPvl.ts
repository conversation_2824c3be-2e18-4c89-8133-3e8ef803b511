import { useFetch } from "@/composables/global/useFetch";

export async function useFetchCertidaoPvl(certidaoId: number, defaultValue = null) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "get", url: requestUrl });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
