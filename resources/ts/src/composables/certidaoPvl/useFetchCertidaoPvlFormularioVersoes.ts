import qs from "qs";
import { useFetch } from "@/composables/global/useFetch";
type Params = {
  certidaoId: number;
  formularioId: number;
  page?: number;
  itemsPerPage?: number;
  filters?: any;
  defaultValue?: any;
};

export async function useFetchCertidaoPvlFormularioVersoes({
  certidaoId,
  formularioId,
  page = 1,
  itemsPerPage = 10,
  filters = null,
  defaultValue = null
}: Params) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/formulario/${formularioId}/versoes`;
  const queryParams = {
    page,
    itemsPerPage,
    ...filters
  };

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({
    method: "get",
    url: requestUrl,
    params: queryParams,
    paramsSerializer: (params: any) => {
      return qs.stringify(params, { arrayFormat: "brackets" });
    }
  });

  return { data, error, isReady, isLoading };
}
