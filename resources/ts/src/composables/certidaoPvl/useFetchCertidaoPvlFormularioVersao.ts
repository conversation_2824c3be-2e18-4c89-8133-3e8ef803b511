import { useFetch } from "@/composables/global/useFetch";

export async function useFetchCertidaoPvlFormularioVersao(
  formularioId: number,
  versaoId: number,
  defaultValue: any = null
) {
  const requestUrl = `/certidao-pvl/certidao/formulario/${formularioId}/versao/${versaoId}`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "get", url: requestUrl });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
