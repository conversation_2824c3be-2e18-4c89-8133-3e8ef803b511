import { useFetch } from "@/composables/global/useFetch";

export async function useFetchCertidaoPvlFormularioRestore(
  certidaoId: number,
  formularioId: number,
  versaoId: number,
  defaultValue: any = null
) {
  const requestUrl = `/certidao-pvl/certidao/${certidaoId}/formulario/${formularioId}/versao/${versaoId}/restore`;

  const { data, error, isReady, isLoading, execute } = useFetch(defaultValue);

  await execute({ method: "post", url: requestUrl, data: defaultValue });

  return {
    data,
    error,
    isReady,
    isLoading
  };
}
