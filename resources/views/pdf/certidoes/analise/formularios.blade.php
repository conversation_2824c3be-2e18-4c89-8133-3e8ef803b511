<!DOCTYPE html>

<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta http-equiv="Content-Style-Type" content="text/css" />
    <title>Formularios de Analise</title>

    <style type="text/css">
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman.ttf') }}) format('truetype');
            font-weight: 400;
            font-style: normal;
         }
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman-bold.ttf') }}) format('truetype');
            font-weight: 700;
            font-style: normal;
         }
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman-italic.ttf') }}) format('truetype');
            font-weight: 400;
            font-style: italic;
         }
        @font-face {
            font-family: 'Times New Roman';
            src: url({{ storage_path('fonts/times-new-roman-bold-italic.ttf') }}) format('truetype');
            font-weight: 700;
            font-style: italic;
         }
    </style>
</head>

<body>
    @foreach($formularios as $formulario)

        @if($formulario->modeloFormulario->capa)

            <section
                class="ai-ck-page ai-ck-page__cover"
                @style( [ "background-image: url('" . public_path('storage/' . $formulario->modeloFormulario->arquivo_capa) . "')" ] )
            >
                <section class="ai-ck-page__cover-wrapper toc-ignore">
                    {!! $formulario->texto !!}
                </section>
            </section>

            <section id="table-of-content">
                <header class="table-of-content__title">Sumário</header>
                <nav id="table-of-content__nav">
                </nav>
            </section>

        @endif

    @endforeach

    @foreach($formularios as $formulario)

        @if(!$formulario->modeloFormulario->capa)

            <section class="ai-ck-page ai-ck-page__content">

                @if($cabecalhoPadrao)
                    <header class="ai-ck-page__content-header--center">
                        {{ $cabecalhoPadrao }}
                    </header>
                @endif

                {!! $formulario->texto !!}

            </section>

        @endif

    @endforeach
</body>
</html>
